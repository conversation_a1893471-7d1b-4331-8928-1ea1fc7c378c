"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const base_node_collector_js_1 = require("./base_node_collector.js");
const constants_js_1 = require("./constants.js");
const utils_js_1 = require("./utils.js");
class NodeModeRepeater extends base_node_collector_js_1.BaseCollectorNode {
    constructor(title) {
        super(title);
        this.inputsPassThroughFollowing = utils_js_1.PassThroughFollowing.ALL;
        this.comfyClass = constants_js_1.NodeTypesString.NODE_MODE_REPEATER;
        this.hasRelayInput = false;
        this.hasTogglerOutput = false;
        this.onConstructed();
    }
    onConstructed() {
        this.addOutput("OPT_CONNECTION", "*", {
            color_on: "#Fc0",
            color_off: "#a80",
        });
        return super.onConstructed();
    }
    onConnectOutput(outputIndex, inputType, inputSlot, inputNode, inputIndex) {
        let canConnect = !this.hasRelayInput;
        canConnect =
            canConnect && super.onConnectOutput(outputIndex, inputType, inputSlot, inputNode, inputIndex);
        let nextNode = (0, utils_js_1.getConnectedOutputNodesAndFilterPassThroughs)(this, inputNode)[0] || inputNode;
        return (canConnect &&
            [
                constants_js_1.NodeTypesString.FAST_MUTER,
                constants_js_1.NodeTypesString.FAST_BYPASSER,
                constants_js_1.NodeTypesString.NODE_COLLECTOR,
                constants_js_1.NodeTypesString.FAST_ACTIONS_BUTTON,
                constants_js_1.NodeTypesString.REROUTE,
                constants_js_1.NodeTypesString.RANDOM_UNMUTER,
            ].includes(nextNode.type || ""));
    }
    onConnectInput(inputIndex, outputType, outputSlot, outputNode, outputIndex) {
        var _a;
        let canConnect = (_a = super.onConnectInput) === null || _a === void 0 ? void 0 : _a.call(this, inputIndex, outputType, outputSlot, outputNode, outputIndex);
        let nextNode = (0, utils_js_1.getConnectedOutputNodesAndFilterPassThroughs)(this, outputNode)[0] || outputNode;
        const isNextNodeRelay = nextNode.type === constants_js_1.NodeTypesString.NODE_MODE_RELAY;
        return canConnect && (!isNextNodeRelay || !this.hasTogglerOutput);
    }
    onConnectionsChange(type, slotIndex, isConnected, linkInfo, ioSlot) {
        super.onConnectionsChange(type, slotIndex, isConnected, linkInfo, ioSlot);
        let hasTogglerOutput = false;
        let hasRelayInput = false;
        const outputNodes = (0, utils_js_1.getConnectedOutputNodesAndFilterPassThroughs)(this);
        for (const outputNode of outputNodes) {
            if ((outputNode === null || outputNode === void 0 ? void 0 : outputNode.type) === constants_js_1.NodeTypesString.FAST_MUTER ||
                (outputNode === null || outputNode === void 0 ? void 0 : outputNode.type) === constants_js_1.NodeTypesString.FAST_BYPASSER) {
                hasTogglerOutput = true;
                break;
            }
        }
        const inputNodes = (0, utils_js_1.getConnectedInputNodesAndFilterPassThroughs)(this);
        for (const [index, inputNode] of inputNodes.entries()) {
            if ((inputNode === null || inputNode === void 0 ? void 0 : inputNode.type) === constants_js_1.NodeTypesString.NODE_MODE_RELAY) {
                if (hasTogglerOutput) {
                    console.log(`Can't be connected to a Relay if also output to a toggler.`);
                    this.disconnectInput(index);
                }
                else {
                    hasRelayInput = true;
                    if (this.inputs[index]) {
                        this.inputs[index].color_on = "#FC0";
                        this.inputs[index].color_off = "#a80";
                    }
                }
            }
            else {
                (0, utils_js_1.changeModeOfNodes)(inputNode, this.mode);
            }
        }
        this.hasTogglerOutput = hasTogglerOutput;
        this.hasRelayInput = hasRelayInput;
        if (this.hasRelayInput) {
            if (this.outputs[0]) {
                this.disconnectOutput(0);
                this.removeOutput(0);
            }
        }
        else if (!this.outputs[0]) {
            this.addOutput("OPT_CONNECTION", "*", {
                color_on: "#Fc0",
                color_off: "#a80",
            });
        }
    }
    onModeChange(from, to) {
        var _a, _b;
        super.onModeChange(from, to);
        const linkedNodes = (0, utils_js_1.getConnectedInputNodesAndFilterPassThroughs)(this).filter((node) => node.type !== constants_js_1.NodeTypesString.NODE_MODE_RELAY);
        if (linkedNodes.length) {
            for (const node of linkedNodes) {
                if (node.type !== constants_js_1.NodeTypesString.NODE_MODE_RELAY) {
                    (0, utils_js_1.changeModeOfNodes)(node, to);
                }
            }
        }
        else if ((_a = app_js_1.app.graph._groups) === null || _a === void 0 ? void 0 : _a.length) {
            for (const group of app_js_1.app.graph._groups) {
                group.recomputeInsideNodes();
                if ((_b = group._nodes) === null || _b === void 0 ? void 0 : _b.includes(this)) {
                    for (const node of group._nodes) {
                        if (node !== this) {
                            (0, utils_js_1.changeModeOfNodes)(node, to);
                        }
                    }
                }
            }
        }
    }
    getHelp() {
        return `
      <p>
        When this node's mode (Mute, Bypass, Active) changes, it will "repeat" that mode to all
        connected input nodes, or, if there are no connected nodes AND it is overlapping a group,
        "repeat" it's mode to all nodes in that group.
      </p>
      <ul>
        <li><p>
          Optionally, connect this mode's output to a ${(0, constants_js_1.stripRgthree)(constants_js_1.NodeTypesString.FAST_MUTER)}
          or ${(0, constants_js_1.stripRgthree)(constants_js_1.NodeTypesString.FAST_BYPASSER)} for a single toggle to quickly
          mute/bypass all its connected nodes.
        </p></li>
        <li><p>
          Optionally, connect a ${(0, constants_js_1.stripRgthree)(constants_js_1.NodeTypesString.NODE_MODE_RELAY)} to this nodes
          inputs to have it automatically toggle its mode. If connected, this will always take
          precedence (and disconnect any connected fast togglers).
        </p></li>
      </ul>
    `;
    }
}
NodeModeRepeater.type = constants_js_1.NodeTypesString.NODE_MODE_REPEATER;
NodeModeRepeater.title = constants_js_1.NodeTypesString.NODE_MODE_REPEATER;
app_js_1.app.registerExtension({
    name: "rgthree.NodeModeRepeater",
    registerCustomNodes() {
        (0, utils_js_1.addConnectionLayoutSupport)(NodeModeRepeater, app_js_1.app, [
            ["Left", "Right"],
            ["Right", "Left"],
        ]);
        LiteGraph.registerNodeType(NodeModeRepeater.type, NodeModeRepeater);
        NodeModeRepeater.category = NodeModeRepeater._category;
    },
});
