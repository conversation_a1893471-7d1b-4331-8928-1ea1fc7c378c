# -*- coding: utf-8 -*-
"""
Get Lora File Text (rgthree) — 仅输出 TEXT；多级 loras_txt + HTTP API（容错版）
"""
from __future__ import annotations
import io
import os
import json
from typing import List, Dict

# 你项目里的工具函数；如果没有，则回退到默认分类/命名
try:
    from ..constants import get_category, get_name  # 如果你项目没有就走 except
except Exception:
    def get_category():
        return "rgthree/utils"
    def get_name(n: str):
        return f"{n} (rgthree)"

# ComfyUI 的路径工具；给出兜底实现，避免导入失败直接崩
try:
    import folder_paths  # ComfyUI
except Exception:
    class _FP:
        models_dir = os.path.join(os.getcwd(), "models")
        @staticmethod
        def get_folder_paths(name: str):
            if name == "loras":
                return [os.path.join(_FP.models_dir, "loras")]
            return []
    folder_paths = _FP()  # type: ignore

# ---------- 路径与工具（全部容错） ----------
def _base_dir() -> str:
    """
    计算 models/loras_txt，保证存在。
    任何异常都回退到 ./models/loras_txt
    """
    try:
        loras_list = folder_paths.get_folder_paths("loras")  # type: ignore
        if isinstance(loras_list, (list, tuple)) and loras_list:
            loras_dir = loras_list[0]
            base = os.path.join(os.path.dirname(loras_dir), "loras_txt")
        else:
            raise RuntimeError("no loras path from folder_paths")
    except Exception:
        models_dir = getattr(folder_paths, "models_dir", None) or os.path.join(os.getcwd(), "models")
        base = os.path.join(models_dir, "loras_txt")
    os.makedirs(base, exist_ok=True)
    return base

def _is_txt(p: str) -> bool: return p.lower().endswith(".txt")

def _abs_in_base(rel: str) -> str:
    base = os.path.abspath(_base_dir())
    p = os.path.abspath(os.path.join(base, rel))
    # 防目录穿越；允许 base 自身
    if not (p == base or p.startswith(base + os.sep)):
        raise RuntimeError(f"path out of loras_txt: {rel}")
    return p

def _list_tree() -> Dict:
    """返回树形结构：{ok:bool, tree?:..., error?:...}"""
    root = _base_dir()
    def walk(d: str) -> Dict:
        children = []
        try:
            names = sorted(os.listdir(d))
        except Exception as e:
            return {"children": [], "error": str(e)}
        for n in names:
            full = os.path.join(d, n)
            rel = os.path.relpath(full, root).replace("\\", "/")
            if os.path.isdir(full):
                item = walk(full)
                item.update({"name": n, "type": "dir", "path": rel})
                children.append(item)
            elif _is_txt(full):
                children.append({"name": n, "type": "file", "path": rel})
        return {"children": children}
    tree = walk(root)
    tree.update({"name": os.path.basename(root) or "loras_txt", "type": "dir", "path": "."})
    return {"ok": True, "tree": tree, "base": root}

def _read(rel: str) -> str:
    p = _abs_in_base(rel)
    if not (os.path.isfile(p) and _is_txt(p)): raise FileNotFoundError(rel)
    with io.open(p, "r", encoding="utf-8", errors="ignore") as f:
        return f.read()

def _write(rel: str, content: str) -> None:
    p = _abs_in_base(rel); os.makedirs(os.path.dirname(p), exist_ok=True)
    with io.open(p, "w", encoding="utf-8") as f: f.write(content or "")

def _delete(rel: str) -> None:
    p = _abs_in_base(rel)
    if os.path.isdir(p): os.rmdir(p)
    elif os.path.isfile(p): os.remove(p)

# ---------- 注册 HTTP 路由（全部 try/except，避免 500） ----------
def _register_http():
    try:
        from server import PromptServer  # type: ignore
    except Exception:
        return
    ps = PromptServer.instance

    @ps.routes.get("/rgthree/loras_txt/ping")
    async def _r_ping(_req): return ps.json({"ok": True})

    @ps.routes.get("/rgthree/loras_txt/list")
    async def _r_list(_req):
        try: return ps.json(_list_tree())
        except Exception as e: return ps.json({"ok": False, "error": f"list failed: {e}"})

    @ps.routes.get("/rgthree/loras_txt/read")
    async def _r_read(req):
        rel = req.query.get("path", "")
        try: return ps.json({"ok": True, "content": _read(rel)})
        except Exception as e: return ps.json({"ok": False, "error": str(e)})

    @ps.routes.post("/rgthree/loras_txt/write")
    async def _r_write(req):
        try:
            data = await req.json(); _write(data.get("path",""), data.get("content",""))
            return ps.json({"ok": True})
        except Exception as e: return ps.json({"ok": False, "error": str(e)})

    @ps.routes.post("/rgthree/loras_txt/delete")
    async def _r_delete(req):
        try:
            data = await req.json(); _delete(data.get("path",""))
            return ps.json({"ok": True})
        except Exception as e: return ps.json({"ok": False, "error": str(e)})

_register_http()

# ---------- 节点：仅输出 TEXT ----------
class GetLoraFileText:
    NAME = get_name("Get Lora File Text")
    CATEGORY = get_category()

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "files": ("STRING", {"default": "[]", "multiline": False}),  # 传入路径数组
                "prefix_text": ("STRING", {"default": "", "multiline": True}),
            }
        }

    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("TEXT",)
    FUNCTION = "run"
    OUTPUT_NODE = False

    def run(self, files: str, prefix_text: str):
        try:
            selected: List[str] = json.loads(files) if files else []
            selected = [p for p in selected if isinstance(p, str) and p and not p.startswith("..")]
        except Exception:
            selected = []
        parts: List[str] = []
        if prefix_text and prefix_text.strip(): parts.append(prefix_text.strip())
        for rel in selected:
            try:
                txt = _read(rel)
                if txt and txt.strip(): parts.append(txt.strip())
            except Exception: pass
        return ("\n\n".join(parts).strip(),)

    @classmethod
    def IS_CHANGED(cls, **_kwargs): return float("nan")

NODE_CLASS_MAPPINGS = { GetLoraFileText.NAME: GetLoraFileText }
NODE_DISPLAY_NAME_MAPPINGS = { GetLoraFileText.NAME: "Get Lora File Text (rgthree)" }
