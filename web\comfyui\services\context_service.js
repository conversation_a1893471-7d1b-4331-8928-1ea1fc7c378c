"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextService = exports.InputMutationOperation = exports.SERVICE = void 0;
exports.stripContextInputPrefixes = stripContextInputPrefixes;
exports.getContextOutputName = getContextOutputName;
const utils_js_1 = require("../utils.js");
const OWNED_PREFIX = "+";
const REGEX_PREFIX = /^[\+⚠️]\s*/;
const REGEX_EMPTY_INPUT = /^\+\s*$/;
function stripContextInputPrefixes(name) {
    return name.replace(REGEX_PREFIX, "");
}
function getContextOutputName(inputName) {
    if (inputName === "base_ctx")
        return "CONTEXT";
    return stripContextInputPrefixes(inputName).toUpperCase();
}
var InputMutationOperation;
(function (InputMutationOperation) {
    InputMutationOperation[InputMutationOperation["UNKNOWN"] = 0] = "UNKNOWN";
    InputMutationOperation[InputMutationOperation["ADDED"] = 1] = "ADDED";
    InputMutationOperation[InputMutationOperation["REMOVED"] = 2] = "REMOVED";
    InputMutationOperation[InputMutationOperation["RENAMED"] = 3] = "RENAMED";
})(InputMutationOperation || (exports.InputMutationOperation = InputMutationOperation = {}));
class ContextService {
    constructor() {
        if (exports.SERVICE) {
            throw new Error("ContextService was already instantiated.");
        }
    }
    onInputChanges(node, mutation) {
        const childCtxs = (0, utils_js_1.getConnectedOutputNodesAndFilterPassThroughs)(node, node, 0);
        for (const childCtx of childCtxs) {
            childCtx.handleUpstreamMutation(mutation);
        }
    }
    getDynamicContextInputsData(node) {
        return node
            .getContextInputsList()
            .map((input, index) => ({
            name: stripContextInputPrefixes(input.name),
            type: String(input.type),
            index,
        }))
            .filter((i) => i.type !== "*");
    }
    getDynamicContextOutputsData(node) {
        return node.outputs.map((output, index) => ({
            name: stripContextInputPrefixes(output.name),
            type: String(output.type),
            index,
        }));
    }
}
exports.ContextService = ContextService;
exports.SERVICE = new ContextService();
