"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const utils_js_1 = require("./utils.js");
const base_power_prompt_js_1 = require("./base_power_prompt.js");
const constants_js_1 = require("./constants.js");
let nodeData = null;
app_js_1.app.registerExtension({
    name: "rgthree.PowerPrompt",
    async beforeRegisterNodeDef(nodeType, passedNodeData) {
        if (passedNodeData.name.includes("Power Prompt") && passedNodeData.name.includes("rgthree")) {
            nodeData = passedNodeData;
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function () {
                onNodeCreated ? onNodeCreated.apply(this, []) : undefined;
                this.powerPrompt = new base_power_prompt_js_1.PowerPrompt(this, passedNodeData);
            };
            (0, utils_js_1.addConnectionLayoutSupport)(nodeType, app_js_1.app, [
                ["Left", "Right"],
                ["Right", "Left"],
            ]);
        }
    },
    async loadedGraphNode(node) {
        if (node.type === constants_js_1.NodeTypesString.POWER_PROMPT) {
            setTimeout(() => {
                if (node.outputs[0].type === "STRING") {
                    if (node.outputs[0].links) {
                        node.outputs[3].links = node.outputs[3].links || [];
                        for (const link of node.outputs[0].links) {
                            node.outputs[3].links.push(link);
                            app_js_1.app.graph.links[link].origin_slot = 3;
                        }
                        node.outputs[0].links = null;
                    }
                    node.outputs[0].type = nodeData.output[0];
                    node.outputs[0].name = nodeData.output_name[0] || node.outputs[0].type;
                    node.outputs[0].color_on = undefined;
                    node.outputs[0].color_off = undefined;
                }
            }, 50);
        }
    },
});
