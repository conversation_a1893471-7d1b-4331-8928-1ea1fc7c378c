"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCollectorNode = void 0;
const rgthree_js_1 = require("./rgthree.js");
const base_any_input_connected_node_js_1 = require("./base_any_input_connected_node.js");
const utils_js_1 = require("./utils.js");
class BaseCollectorNode extends base_any_input_connected_node_js_1.BaseAnyInputConnectedNode {
    constructor(title) {
        super(title);
        this.inputsPassThroughFollowing = utils_js_1.PassThroughFollowing.REROUTE_ONLY;
        this.logger = rgthree_js_1.rgthree.newLogSession("[BaseCollectorNode]");
    }
    clone() {
        const cloned = super.clone();
        return cloned;
    }
    handleLinkedNodesStabilization(linkedNodes) {
        return false;
    }
    onConnectInput(inputIndex, outputType, outputSlot, outputNode, outputIndex) {
        var _a, _b, _c, _d;
        let canConnect = super.onConnectInput(inputIndex, outputType, outputSlot, outputNode, outputIndex);
        if (canConnect) {
            const allConnectedNodes = (0, utils_js_1.getConnectedInputNodes)(this);
            const nodesAlreadyInSlot = (0, utils_js_1.getConnectedInputNodes)(this, undefined, inputIndex);
            if (allConnectedNodes.includes(outputNode)) {
                const [n, v] = this.logger.debugParts(`${outputNode.title} is already connected to ${this.title}.`);
                (_a = console[n]) === null || _a === void 0 ? void 0 : _a.call(console, ...v);
                if (nodesAlreadyInSlot.includes(outputNode)) {
                    const [n, v] = this.logger.debugParts(`... but letting it slide since it's for the same slot.`);
                    (_b = console[n]) === null || _b === void 0 ? void 0 : _b.call(console, ...v);
                }
                else {
                    canConnect = false;
                }
            }
            if (canConnect && (0, utils_js_1.shouldPassThrough)(outputNode, utils_js_1.PassThroughFollowing.REROUTE_ONLY)) {
                const connectedNode = (0, utils_js_1.getConnectedInputNodesAndFilterPassThroughs)(outputNode, undefined, undefined, utils_js_1.PassThroughFollowing.REROUTE_ONLY)[0];
                if (connectedNode && allConnectedNodes.includes(connectedNode)) {
                    const [n, v] = this.logger.debugParts(`${connectedNode.title} is already connected to ${this.title}.`);
                    (_c = console[n]) === null || _c === void 0 ? void 0 : _c.call(console, ...v);
                    if (nodesAlreadyInSlot.includes(connectedNode)) {
                        const [n, v] = this.logger.debugParts(`... but letting it slide since it's for the same slot.`);
                        (_d = console[n]) === null || _d === void 0 ? void 0 : _d.call(console, ...v);
                    }
                    else {
                        canConnect = false;
                    }
                }
            }
        }
        return canConnect;
    }
}
exports.BaseCollectorNode = BaseCollectorNode;
