"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RgthreeConfigDialog = void 0;
const app_js_1 = require("scripts/app.js");
const dialog_js_1 = require("rgthree/common/dialog.js");
const utils_dom_js_1 = require("rgthree/common/utils_dom.js");
const svgs_js_1 = require("rgthree/common/media/svgs.js");
const rgthree_js_1 = require("./rgthree.js");
const config_service_js_1 = require("./services/config_service.js");
var ConfigType;
(function (ConfigType) {
    ConfigType[ConfigType["UNKNOWN"] = 0] = "UNKNOWN";
    ConfigType[ConfigType["BOOLEAN"] = 1] = "BOOLEAN";
    ConfigType[ConfigType["STRING"] = 2] = "STRING";
    ConfigType[ConfigType["NUMBER"] = 3] = "NUMBER";
    ConfigType[ConfigType["ARRAY"] = 4] = "ARRAY";
})(ConfigType || (ConfigType = {}));
var ConfigInputType;
(function (ConfigInputType) {
    ConfigInputType[ConfigInputType["UNKNOWN"] = 0] = "UNKNOWN";
    ConfigInputType[ConfigInputType["CHECKLIST"] = 1] = "CHECKLIST";
})(ConfigInputType || (ConfigInputType = {}));
const TYPE_TO_STRING = {
    [ConfigType.UNKNOWN]: "unknown",
    [ConfigType.BOOLEAN]: "boolean",
    [ConfigType.STRING]: "string",
    [ConfigType.NUMBER]: "number",
    [ConfigType.ARRAY]: "array",
};
const CONFIGURABLE = {
    features: [
        {
            key: "features.progress_bar.enabled",
            type: ConfigType.BOOLEAN,
            label: "Prompt Progress Bar",
            description: `Shows a minimal progress bar for nodes and steps at the top of the app.`,
            subconfig: [
                {
                    key: "features.progress_bar.height",
                    type: ConfigType.NUMBER,
                    label: "Height of the bar",
                },
                {
                    key: "features.progress_bar.position",
                    type: ConfigType.STRING,
                    label: "Position at top or bottom of window",
                    options: ["top", "bottom"],
                },
            ],
        },
        {
            key: "features.import_individual_nodes.enabled",
            type: ConfigType.BOOLEAN,
            label: "Import Individual Nodes Widgets",
            description: "Dragging & Dropping a similar image/JSON workflow onto (most) current workflow nodes" +
                "will allow you to import that workflow's node's widgets when it has the same " +
                "id and type. This is useful when you have several images and you'd like to import just " +
                "one part of a previous iteration, like a seed, or prompt.",
        },
    ],
    menus: [
        {
            key: "features.comfy_top_bar_menu.enabled",
            type: ConfigType.BOOLEAN,
            label: "Enable Top Bar Menu",
            description: "Have quick access from ComfyUI's new top bar to rgthree-comfy bookmarks, settings " +
                "(and more to come).",
        },
        {
            key: "features.menu_queue_selected_nodes",
            type: ConfigType.BOOLEAN,
            label: "Show 'Queue Selected Output Nodes'",
            description: "Will show a menu item in the right-click context menus to queue (only) the selected " +
                "output nodes.",
        },
        {
            key: "features.menu_auto_nest.subdirs",
            type: ConfigType.BOOLEAN,
            label: "Auto Nest Subdirectories in Menus",
            description: "When a large, flat list of values contain sub-directories, auto nest them. (Like, for " +
                "a large list of checkpoints).",
            subconfig: [
                {
                    key: "features.menu_auto_nest.threshold",
                    type: ConfigType.NUMBER,
                    label: "Number of items needed to trigger nesting.",
                },
            ],
        },
        {
            key: "features.menu_bookmarks.enabled",
            type: ConfigType.BOOLEAN,
            label: "Show Bookmarks in context menu",
            description: "Will list bookmarks in the rgthree-comfy right-click context menu.",
        },
    ],
    groups: [
        {
            key: "features.group_header_fast_toggle.enabled",
            type: ConfigType.BOOLEAN,
            label: "Show fast toggles in Group Headers",
            description: "Show quick toggles in Groups' Headers to quickly mute, bypass or queue.",
            subconfig: [
                {
                    key: "features.group_header_fast_toggle.toggles",
                    type: ConfigType.ARRAY,
                    label: "Which toggles to show.",
                    inputType: ConfigInputType.CHECKLIST,
                    options: [
                        { value: "queue", label: "queue" },
                        { value: "bypass", label: "bypass" },
                        { value: "mute", label: "mute" },
                    ],
                },
                {
                    key: "features.group_header_fast_toggle.show",
                    type: ConfigType.STRING,
                    label: "When to show them.",
                    options: [
                        { value: "hover", label: "on hover" },
                        { value: "always", label: "always" },
                    ],
                },
            ],
        },
    ],
    advanced: [
        {
            key: "features.show_alerts_for_corrupt_workflows",
            type: ConfigType.BOOLEAN,
            label: "Detect Corrupt Workflows",
            description: "Will show a message at the top of the screen when loading a workflow that has " +
                "corrupt linking data.",
        },
        {
            key: "log_level",
            type: ConfigType.STRING,
            label: "Log level for browser dev console.",
            description: "Further down the list, the more verbose logs to the console will be. For instance, " +
                "selecting 'IMPORTANT' means only important message will be logged to the browser " +
                "console, while selecting 'WARN' will log all messages at or higher than WARN, including " +
                "'ERROR' and 'IMPORTANT' etc.",
            options: ["IMPORTANT", "ERROR", "WARN", "INFO", "DEBUG", "DEV"],
            isDevOnly: true,
            onSave: function (value) {
                rgthree_js_1.rgthree.setLogLevel(value);
            },
        },
        {
            key: "features.invoke_extensions_async.node_created",
            type: ConfigType.BOOLEAN,
            label: "Allow other extensions to call nodeCreated on rgthree-nodes.",
            isDevOnly: true,
            description: "Do not disable unless you are having trouble (and then file an issue at rgthree-comfy)." +
                "Prior to Apr 2024 it was not possible for other extensions to invoke their nodeCreated " +
                "event on some rgthree-comfy nodes. Now it's possible and this option is only here in " +
                "for easy if something is wrong.",
        },
    ],
};
function fieldrow(item) {
    var _a;
    const initialValue = config_service_js_1.SERVICE.getConfigValue(item.key);
    const container = (0, utils_dom_js_1.createElement)(`div.fieldrow.-type-${TYPE_TO_STRING[item.type]}`, {
        dataset: {
            name: item.key,
            initial: initialValue,
            type: item.type,
        },
    });
    (0, utils_dom_js_1.createElement)(`label[for="${item.key}"]`, {
        children: [
            (0, utils_dom_js_1.createElement)(`span[text="${item.label}"]`),
            item.description ? (0, utils_dom_js_1.createElement)("small", { html: item.description }) : null,
        ],
        parent: container,
    });
    let input;
    if ((_a = item.options) === null || _a === void 0 ? void 0 : _a.length) {
        if (item.inputType === ConfigInputType.CHECKLIST) {
            const initialValueList = initialValue || [];
            input = (0, utils_dom_js_1.createElement)(`fieldset.rgthree-checklist-group[id="${item.key}"]`, {
                parent: container,
                children: item.options.map((o) => {
                    const label = o.label || String(o);
                    const value = o.value || o;
                    const id = `${item.key}_${value}`;
                    return (0, utils_dom_js_1.createElement)(`span.rgthree-checklist-item`, {
                        children: [
                            (0, utils_dom_js_1.createElement)(`input[type="checkbox"][value="${value}"]`, {
                                id,
                                checked: initialValueList.includes(value),
                            }),
                            (0, utils_dom_js_1.createElement)(`label`, {
                                for: id,
                                text: label,
                            })
                        ]
                    });
                }),
            });
        }
        else {
            input = (0, utils_dom_js_1.createElement)(`select[id="${item.key}"]`, {
                parent: container,
                children: item.options.map((o) => {
                    const label = o.label || String(o);
                    const value = o.value || o;
                    const valueSerialized = JSON.stringify({ value: value });
                    return (0, utils_dom_js_1.createElement)(`option[value="${valueSerialized}"]`, {
                        text: label,
                        selected: valueSerialized === JSON.stringify({ value: initialValue }),
                    });
                }),
            });
        }
    }
    else if (item.type === ConfigType.BOOLEAN) {
        container.classList.toggle("-checked", !!initialValue);
        input = (0, utils_dom_js_1.createElement)(`input[type="checkbox"][id="${item.key}"]`, {
            parent: container,
            checked: initialValue,
        });
    }
    else {
        input = (0, utils_dom_js_1.createElement)(`input[id="${item.key}"]`, {
            parent: container,
            value: initialValue,
        });
    }
    (0, utils_dom_js_1.createElement)("div.fieldrow-value", { children: [input], parent: container });
    return container;
}
class RgthreeConfigDialog extends dialog_js_1.RgthreeDialog {
    constructor() {
        const content = (0, utils_dom_js_1.createElement)("div");
        content.appendChild(RgthreeConfigDialog.buildFieldset(CONFIGURABLE["features"], "Features"));
        content.appendChild(RgthreeConfigDialog.buildFieldset(CONFIGURABLE["menus"], "Menus"));
        content.appendChild(RgthreeConfigDialog.buildFieldset(CONFIGURABLE["groups"], "Groups"));
        content.appendChild(RgthreeConfigDialog.buildFieldset(CONFIGURABLE["advanced"], "Advanced"));
        content.addEventListener("input", (e) => {
            const changed = this.getChangedFormData();
            (0, utils_dom_js_1.queryAll)(".save-button", this.element)[0].disabled =
                !Object.keys(changed).length;
        });
        content.addEventListener("change", (e) => {
            const changed = this.getChangedFormData();
            (0, utils_dom_js_1.queryAll)(".save-button", this.element)[0].disabled =
                !Object.keys(changed).length;
        });
        const dialogOptions = {
            class: "-iconed -settings",
            title: svgs_js_1.logoRgthree + `<h2>Settings - rgthree-comfy</h2>`,
            content,
            onBeforeClose: () => {
                const changed = this.getChangedFormData();
                if (Object.keys(changed).length) {
                    return confirm("Looks like there are unsaved changes. Are you sure you want close?");
                }
                return true;
            },
            buttons: [
                {
                    label: "Save",
                    disabled: true,
                    className: "rgthree-button save-button -blue",
                    callback: async (e) => {
                        var _a, _b;
                        const changed = this.getChangedFormData();
                        if (!Object.keys(changed).length) {
                            this.close();
                            return;
                        }
                        const success = await config_service_js_1.SERVICE.setConfigValues(changed);
                        if (success) {
                            for (const key of Object.keys(changed)) {
                                (_b = (_a = Object.values(CONFIGURABLE)
                                    .flat()
                                    .find((f) => f.key === key)) === null || _a === void 0 ? void 0 : _a.onSave) === null || _b === void 0 ? void 0 : _b.call(_a, changed[key]);
                            }
                            this.close();
                            rgthree_js_1.rgthree.showMessage({
                                id: "config-success",
                                message: `${svgs_js_1.checkmark} Successfully saved rgthree-comfy settings!`,
                                timeout: 4000,
                            });
                            (0, utils_dom_js_1.queryAll)(".save-button", this.element)[0].disabled = true;
                        }
                        else {
                            alert("There was an error saving rgthree-comfy configuration.");
                        }
                    },
                },
            ],
        };
        super(dialogOptions);
    }
    static buildFieldset(datas, label) {
        const fieldset = (0, utils_dom_js_1.createElement)(`fieldset`, { children: [(0, utils_dom_js_1.createElement)(`legend[text="${label}"]`)] });
        for (const data of datas) {
            if (data.isDevOnly && !rgthree_js_1.rgthree.isDevMode()) {
                continue;
            }
            const container = (0, utils_dom_js_1.createElement)("div.formrow");
            container.appendChild(fieldrow(data));
            if (data.subconfig) {
                for (const subfeature of data.subconfig) {
                    container.appendChild(fieldrow(subfeature));
                }
            }
            fieldset.appendChild(container);
        }
        return fieldset;
    }
    getChangedFormData() {
        return (0, utils_dom_js_1.queryAll)("[data-name]", this.contentElement).reduce((acc, el) => {
            const name = el.dataset["name"];
            const type = el.dataset["type"];
            const initialValue = config_service_js_1.SERVICE.getConfigValue(name);
            let currentValueEl = (0, utils_dom_js_1.queryAll)("fieldset.rgthree-checklist-group, input, textarea, select", el)[0];
            let currentValue = null;
            if (type === String(ConfigType.BOOLEAN)) {
                currentValue = currentValueEl.checked;
                el.classList.toggle("-checked", currentValue);
            }
            else {
                currentValue = currentValueEl === null || currentValueEl === void 0 ? void 0 : currentValueEl.value;
                if (currentValueEl.nodeName === "SELECT") {
                    currentValue = JSON.parse(currentValue).value;
                }
                else if (currentValueEl.classList.contains('rgthree-checklist-group')) {
                    currentValue = [];
                    for (const check of (0, utils_dom_js_1.queryAll)('input[type="checkbox"]', currentValueEl)) {
                        if (check.checked) {
                            currentValue.push(check.value);
                        }
                    }
                }
                else if (type === String(ConfigType.NUMBER)) {
                    currentValue = Number(currentValue) || initialValue;
                }
            }
            if (JSON.stringify(currentValue) !== JSON.stringify(initialValue)) {
                acc[name] = currentValue;
            }
            return acc;
        }, {});
    }
}
exports.RgthreeConfigDialog = RgthreeConfigDialog;
app_js_1.app.ui.settings.addSetting({
    id: "rgthree.config",
    defaultValue: null,
    name: "Open rgthree-comfy config",
    type: () => {
        return (0, utils_dom_js_1.createElement)("tr.rgthree-comfyui-settings-row", {
            children: [
                (0, utils_dom_js_1.createElement)("td", {
                    child: `<div>${svgs_js_1.logoRgthree} [rgthree-comfy] configuration / settings</div>`,
                }),
                (0, utils_dom_js_1.createElement)("td", {
                    child: (0, utils_dom_js_1.createElement)('button.rgthree-button.-blue[text="rgthree-comfy settings"]', {
                        events: {
                            click: (e) => {
                                new RgthreeConfigDialog().show();
                            },
                        },
                    }),
                }),
            ],
        });
    },
});
