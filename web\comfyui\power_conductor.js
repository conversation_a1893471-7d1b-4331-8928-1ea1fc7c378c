"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _ComfyNodeWrapper_id, _ComfyWidgetWrapper_widget;
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const py_parser_js_1 = require("rgthree/common/py_parser.js");
const base_node_js_1 = require("./base_node.js");
const utils_widgets_js_1 = require("./utils_widgets.js");
const constants_js_1 = require("./constants.js");
const widgets_js_1 = require("scripts/widgets.js");
const config_service_js_1 = require("./services/config_service.js");
const utils_js_1 = require("./utils.js");
const BUILT_INS = {
    node: {
        fn: (query) => {
            if (typeof query === "number" || /^\d+(\.\d+)?/.exec(query)) {
                return new ComfyNodeWrapper(Number(query));
            }
            return null;
        },
    },
};
class RgthreePowerConductor extends base_node_js_1.RgthreeBaseVirtualNode {
    constructor(title = RgthreePowerConductor.title) {
        super(title);
        this.comfyClass = constants_js_1.NodeTypesString.POWER_CONDUCTOR;
        this.serialize_widgets = true;
        this.codeWidget = widgets_js_1.ComfyWidgets.STRING(this, "", ["STRING", { multiline: true }], app_js_1.app).widget;
        this.addCustomWidget(this.codeWidget);
        (this.buttonWidget = new utils_widgets_js_1.RgthreeBetterButtonWidget("Run", (...args) => {
            this.execute();
        })),
            this.addCustomWidget(this.buttonWidget);
        this.onConstructed();
    }
    execute() {
        (0, py_parser_js_1.execute)(this.codeWidget.value, {}, BUILT_INS);
    }
}
RgthreePowerConductor.title = constants_js_1.NodeTypesString.POWER_CONDUCTOR;
RgthreePowerConductor.type = constants_js_1.NodeTypesString.POWER_CONDUCTOR;
const NODE_CLASS = RgthreePowerConductor;
class ComfyNodeWrapper {
    constructor(id) {
        _ComfyNodeWrapper_id.set(this, void 0);
        __classPrivateFieldSet(this, _ComfyNodeWrapper_id, id, "f");
    }
    getNode() {
        return app_js_1.app.graph.getNodeById(__classPrivateFieldGet(this, _ComfyNodeWrapper_id, "f"));
    }
    get id() {
        return this.getNode().id;
    }
    get title() {
        return this.getNode().title;
    }
    set title(value) {
        this.getNode().title = value;
    }
    get widgets() {
        var _a;
        return new py_parser_js_1.PyTuple((_a = this.getNode().widgets) === null || _a === void 0 ? void 0 : _a.map((w) => new ComfyWidgetWrapper(w)));
    }
    get mode() {
        return this.getNode().mode;
    }
    mute() {
        (0, utils_js_1.changeModeOfNodes)(this.getNode(), 2);
    }
    bypass() {
        (0, utils_js_1.changeModeOfNodes)(this.getNode(), 4);
    }
    enable() {
        (0, utils_js_1.changeModeOfNodes)(this.getNode(), 0);
    }
}
_ComfyNodeWrapper_id = new WeakMap();
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "id", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "title", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "widgets", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "mode", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "mute", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "bypass", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyNodeWrapper.prototype, "enable", null);
class ComfyWidgetWrapper {
    constructor(widget) {
        _ComfyWidgetWrapper_widget.set(this, void 0);
        __classPrivateFieldSet(this, _ComfyWidgetWrapper_widget, widget, "f");
    }
    get value() {
        return __classPrivateFieldGet(this, _ComfyWidgetWrapper_widget, "f").value;
    }
    get label() {
        return __classPrivateFieldGet(this, _ComfyWidgetWrapper_widget, "f").label;
    }
    toggle(value) {
        if (typeof __classPrivateFieldGet(this, _ComfyWidgetWrapper_widget, "f")["toggle"] === "function") {
            __classPrivateFieldGet(this, _ComfyWidgetWrapper_widget, "f")["toggle"](value);
        }
        else {
        }
    }
}
_ComfyWidgetWrapper_widget = new WeakMap();
__decorate([
    py_parser_js_1.Exposed
], ComfyWidgetWrapper.prototype, "value", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyWidgetWrapper.prototype, "label", null);
__decorate([
    py_parser_js_1.Exposed
], ComfyWidgetWrapper.prototype, "toggle", null);
app_js_1.app.registerExtension({
    name: "rgthree.PowerConductor",
    registerCustomNodes() {
        if (config_service_js_1.SERVICE.getConfigValue("unreleased.power_conductor.enabled")) {
            NODE_CLASS.setUp();
        }
    },
});
