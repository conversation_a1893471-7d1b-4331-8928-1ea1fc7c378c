"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelsInfoPage = void 0;
const utils_dom_js_1 = require("../common/utils_dom.js");
const rgthree_api_js_1 = require("rgthree/common/rgthree_api.js");
const model_info_card_js_1 = require("./components/model-info-card.js");
function parseQuery(query) {
    const matches = query.match(/"[^\"]+"/g) || [];
    for (const match of matches) {
        let cleaned = match.substring(1, match.length - 1);
        cleaned = cleaned.replace(/\s+/g, " ").trim().replace(/\s/g, "__SPACE__");
        query = query.replace(match, ` ${cleaned} `);
    }
    const queryParts = query
        .replace(/\s+/g, " ")
        .trim()
        .split(" ")
        .map((p) => p.replace(/__SPACE__/g, " "));
    return queryParts;
}
class ModelsInfoPage {
    constructor() {
        this.selectBaseModel = (0, utils_dom_js_1.createElement)('select[name="baseModel"][on="change:filter"]');
        this.searchbox = (0, utils_dom_js_1.query)("#searchbox");
        this.modelsList = (0, utils_dom_js_1.query)("#models-list");
        this.queryLast = "";
        this.doSearchDebounce = 0;
        console.log("hello model page");
        this.init();
    }
    async init() {
        this.searchbox.addEventListener("input", (e) => {
            if (this.doSearchDebounce) {
                return;
            }
            this.doSearchDebounce = setTimeout(() => {
                this.doSearch();
                this.doSearchDebounce = 0;
            }, 250);
        });
        const loras = await rgthree_api_js_1.rgthreeApi.getLorasInfo({ light: true });
        console.log(loras);
        const baseModels = new Set();
        for (const lora of loras) {
            const el = model_info_card_js_1.RgthreeModelInfoCard.create();
            el.setModelData(lora);
            el.bindWhenConnected(lora);
            console.log(el);
            lora.baseModel && baseModels.add(lora.baseModel);
            this.modelsList.appendChild((0, utils_dom_js_1.createElement)("li.model-item", { child: el }));
        }
        if (baseModels.size > 1) {
            (0, utils_dom_js_1.createElement)(`option[value="ALL"][text="Choose base model."]`, {
                parent: this.selectBaseModel,
            });
            for (const baseModel of baseModels.values()) {
                (0, utils_dom_js_1.createElement)(`option[value="${baseModel}"][text="${baseModel}"]`, {
                    parent: this.selectBaseModel,
                });
            }
            this.searchbox.insertAdjacentElement("afterend", this.selectBaseModel);
        }
        const data = (0, utils_dom_js_1.getActionEls)(document.body);
        for (const dataItem of Object.values(data)) {
            for (const [event, action] of Object.entries(dataItem.actions)) {
                dataItem.el.addEventListener(event, (e) => {
                    if (typeof this[action] != "function") {
                        throw new Error(`"${action}" does not exist on instance.`);
                    }
                    this[action](e);
                });
            }
        }
    }
    filter() {
        const parts = parseQuery(this.queryLast);
        const baseModel = this.selectBaseModel.value;
        const els = (0, utils_dom_js_1.queryAll)(model_info_card_js_1.RgthreeModelInfoCard.NAME);
        const shouldHide = (el) => {
            let hide = baseModel !== "ALL" && !el.hasBaseModel(baseModel);
            if (!hide) {
                for (let part of parts) {
                    let negate = false;
                    if (part.startsWith("-")) {
                        negate = true;
                        part = part.substring(1);
                    }
                    if (!part)
                        continue;
                    if (part.startsWith("has:")) {
                        if (part === "has:civitai") {
                            hide = !el.hasData(part.replace("has:", ""));
                        }
                    }
                    else {
                        hide = !el.matchesQueryText(part);
                    }
                    hide = negate ? !hide : hide;
                    if (hide) {
                        break;
                    }
                }
            }
            return hide;
        };
        for (const el of els) {
            const hide = shouldHide(el);
            if (hide) {
                el.hide();
            }
            else {
                el.show();
            }
        }
        console.log("filter");
    }
    doSearch() {
        const query = this.searchbox.value.trim();
        if (this.queryLast != query) {
            this.queryLast = query;
            this.filter();
        }
    }
}
exports.ModelsInfoPage = ModelsInfoPage;
