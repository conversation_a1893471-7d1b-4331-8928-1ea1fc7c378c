"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const base_node_mode_changer_js_1 = require("./base_node_mode_changer.js");
const constants_js_1 = require("./constants.js");
const MODE_BYPASS = 4;
const MODE_ALWAYS = 0;
class BypasserNode extends base_node_mode_changer_js_1.BaseNodeModeChanger {
    constructor(title = BypasserNode.title) {
        super(title);
        this.comfyClass = constants_js_1.NodeTypesString.FAST_BYPASSER;
        this.modeOn = MODE_ALWAYS;
        this.modeOff = MODE_BYPASS;
        this.onConstructed();
    }
    async handleAction(action) {
        if (action === "Bypass all") {
            for (const widget of this.widgets || []) {
                this.forceWidgetOff(widget, true);
            }
        }
        else if (action === "Enable all") {
            for (const widget of this.widgets || []) {
                this.forceWidgetOn(widget, true);
            }
        }
        else if (action === "Toggle all") {
            for (const widget of this.widgets || []) {
                this.forceWidgetToggle(widget, true);
            }
        }
    }
}
BypasserNode.exposedActions = ["Bypass all", "Enable all", "Toggle all"];
BypasserNode.type = constants_js_1.NodeTypesString.FAST_BYPASSER;
BypasserNode.title = constants_js_1.NodeTypesString.FAST_BYPASSER;
app_js_1.app.registerExtension({
    name: "rgthree.Bypasser",
    registerCustomNodes() {
        BypasserNode.setUp();
    },
    loadedGraphNode(node) {
        if (node.type == BypasserNode.title) {
            node._tempWidth = node.size[0];
        }
    },
});
