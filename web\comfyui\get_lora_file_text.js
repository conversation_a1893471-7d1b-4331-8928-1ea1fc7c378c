/*
 * Get Lora File Text (rgthree) — 前端扩展
 * 功能：
 *  - 左侧树形展示 models/loras_txt 下的所有 .txt（多级目录）
 *  - 勾选多选，可拖动排序（上/下按钮）
 *  - 右键菜单：新增 txt、删除、刷新、在资源管理器中打开、预览
 *  - "前缀文本"输入框，提交给 Python 的 prefix_text
 *  - 将勾选结果同步到隐藏的 `files`(STRING) 输入（JSON 数组）
 */
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import { app } from "../../scripts/app.js";
// @ts-ignore
import { ComfyWidgets } from "../../scripts/widgets.js";
const API = {
    list: "/rgthree/loras_txt/list",
    read: "/rgthree/loras_txt/read",
    write: "/rgthree/loras_txt/write",
    del: "/rgthree/loras_txt/delete",
};
async function httpGet(url) {
    const r = await fetch(url, { credentials: "include" });
    if (!r.ok)
        throw new Error(await r.text());
    return (await r.json());
}
async function httpPost(url, data) {
    const r = await fetch(url, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data !== null && data !== void 0 ? data : {}),
    });
    if (!r.ok)
        throw new Error(await r.text());
    return (await r.json());
}
function buildTreeHTML(tree, onPick) {
    const root = document.createElement("div");
    root.className = "glft-tree";
    function makeNode(n) {
        const li = document.createElement("div");
        li.className = `glft-node glft-${n.type}`;
        const title = document.createElement("div");
        title.className = "glft-title";
        title.textContent = n.name;
        title.oncontextmenu = (e) => {
            e.preventDefault();
            showContextMenu(e.clientX, e.clientY, n);
        };
        title.onclick = () => {
            if (n.type === "file")
                onPick(n);
            li.classList.toggle("glft-open");
        };
        li.appendChild(title);
        if (n.children && n.children.length) {
            const box = document.createElement("div");
            box.className = "glft-children";
            for (const c of n.children)
                box.appendChild(makeNode(c));
            li.appendChild(box);
        }
        return li;
    }
    root.appendChild(makeNode(tree));
    return root;
}
function showContextMenu(x, y, node) {
    var _a;
    const id = "glft-menu";
    (_a = document.getElementById(id)) === null || _a === void 0 ? void 0 : _a.remove();
    const menu = document.createElement("div");
    menu.id = id;
    menu.className = "glft-menu";
    const items = [];
    items.push(["刷新", () => dispatchEvent(new CustomEvent("glft:refresh"))]);
    if (node.type === "file") {
        items.push(["预览", () => dispatchEvent(new CustomEvent("glft:preview", { detail: node }))]);
        items.push(["删除", () => dispatchEvent(new CustomEvent("glft:delete", { detail: node }))]);
    }
    if (node.type === "dir") {
        items.push([
            "新增 txt",
            () => dispatchEvent(new CustomEvent("glft:new", { detail: node })),
        ]);
    }
    items.push([
        "在资源管理器中打开",
        () => dispatchEvent(new CustomEvent("glft:reveal", { detail: node })),
    ]);
    for (const [text, cb] of items) {
        const it = document.createElement("div");
        it.className = "glft-menu-item";
        it.textContent = text;
        it.onclick = () => {
            cb();
            menu.remove();
        };
        menu.appendChild(it);
    }
    menu.style.left = x + "px";
    menu.style.top = y + "px";
    document.body.appendChild(menu);
    document.addEventListener("click", () => {
        menu.remove();
    }, { once: true });
}
function createSelectedList(container, onChange) {
    const wrap = document.createElement("div");
    wrap.className = "glft-selected";
    const list = document.createElement("div");
    list.className = "glft-selected-list";
    wrap.appendChild(list);
    function render(items) {
        list.innerHTML = "";
        for (let i = 0; i < items.length; i++) {
            const row = document.createElement("div");
            row.className = "glft-row";
            const name = document.createElement("div");
            name.className = "glft-row-name";
            name.textContent = items[i];
            row.appendChild(name);
            const btns = document.createElement("div");
            btns.className = "glft-row-btns";
            const up = document.createElement("button");
            up.textContent = "↑";
            up.title = "上移";
            up.onclick = () => {
                if (i > 0) {
                    const t = items[i - 1];
                    items[i - 1] = items[i];
                    items[i] = t;
                    render(items);
                    onChange(items.slice());
                }
            };
            const down = document.createElement("button");
            down.textContent = "↓";
            down.title = "下移";
            down.onclick = () => {
                if (i < items.length - 1) {
                    const t = items[i + 1];
                    items[i + 1] = items[i];
                    items[i] = t;
                    render(items);
                    onChange(items.slice());
                }
            };
            const del = document.createElement("button");
            del.textContent = "✕";
            del.title = "移除";
            del.onclick = () => {
                items.splice(i, 1);
                render(items);
                onChange(items.slice());
            };
            btns.appendChild(up);
            btns.appendChild(down);
            btns.appendChild(del);
            row.appendChild(btns);
            list.appendChild(row);
        }
    }
    container.appendChild(wrap);
    return {
        render,
    };
}
// ---------- 注册扩展 ----------
app.registerExtension({
    name: "rgthree.get_lora_file_text",
    async beforeRegisterNodeDef(nodeDef) {
        const NODE_NAME = "Get Lora File Text (rgthree)";
        if ((nodeDef === null || nodeDef === void 0 ? void 0 : nodeDef.name) !== NODE_NAME)
            return;
        const orig = nodeDef.prototype.onNodeCreated;
        nodeDef.prototype.onNodeCreated = function (...args) {
            var _a, _b, _c, _d, _e, _f, _g;
            orig === null || orig === void 0 ? void 0 : orig.apply(this, args);
            // 拿到 widgets：files（隐藏）, prefix_text（可见）
            const wFiles = (_a = this.widgets) === null || _a === void 0 ? void 0 : _a.find((w) => w.name === "files");
            const wPrefix = (_b = this.widgets) === null || _b === void 0 ? void 0 : _b.find((w) => w.name === "prefix_text");
            if (!wFiles) {
                // 万一没被生成，就补一个隐藏的
                ComfyWidgets.STRING(this, "files", ["STRING", { multiline: false }], app).widget;
            }
            const filesWidget = (_c = this.widgets) === null || _c === void 0 ? void 0 : _c.find((w) => w.name === "files");
            if (filesWidget) {
                filesWidget.hidden = true; // 隐藏 JSON 字段
            }
            if (wPrefix) {
                wPrefix.label = "前缀文本 (会拼接在最前面)";
            }
            // ---------- 自定义 UI 容器 ----------
            const panel = document.createElement("div");
            panel.className = "glft-panel";
            const left = document.createElement("div");
            left.className = "glft-left";
            const right = document.createElement("div");
            right.className = "glft-right";
            const titleL = document.createElement("div");
            titleL.className = "glft-head";
            titleL.textContent = "loras_txt 树形文件 (右键更多操作)";
            left.appendChild(titleL);
            const titleR = document.createElement("div");
            titleR.className = "glft-head";
            titleR.textContent = "已选择（顺序可调整；输出合并顺序=列表顺序）";
            right.appendChild(titleR);
            const treeBox = document.createElement("div");
            treeBox.className = "glft-treebox";
            left.appendChild(treeBox);
            const selected = createSelectedList(right, (paths) => {
                var _a;
                if (filesWidget) {
                    filesWidget.value = JSON.stringify(paths);
                    (_a = filesWidget.callback) === null || _a === void 0 ? void 0 : _a.call(filesWidget, filesWidget.value);
                }
            });
            panel.appendChild(left);
            panel.appendChild(right);
            // 将自定义 DOM 挂到 node
            // @ts-ignore
            ((_d = this.addDOMWidget) === null || _d === void 0 ? void 0 : _d.call(this, panel)) || ((_f = (_e = this.dom) === null || _e === void 0 ? void 0 : _e.content) === null || _f === void 0 ? void 0 : _f.appendChild(panel));
            // ---------- 行为 ----------
            let currentPaths = [];
            try {
                currentPaths = JSON.parse((_g = filesWidget === null || filesWidget === void 0 ? void 0 : filesWidget.value) !== null && _g !== void 0 ? _g : "[]");
            }
            catch {
                currentPaths = [];
            }
            selected.render(currentPaths.slice());
            async function renderTree() {
                const data = (await httpGet(API.list));
                treeBox.innerHTML = "";
                const el = buildTreeHTML(data, (node) => {
                    var _a;
                    if (!currentPaths.includes(node.path)) {
                        currentPaths.push(node.path);
                        selected.render(currentPaths);
                        if (filesWidget) {
                            filesWidget.value = JSON.stringify(currentPaths);
                            (_a = filesWidget.callback) === null || _a === void 0 ? void 0 : _a.call(filesWidget, filesWidget.value);
                        }
                    }
                });
                treeBox.appendChild(el);
            }
            // 初次渲染
            renderTree();
            // 刷新
            window.addEventListener("glft:refresh", () => renderTree());
            // 预览
            window.addEventListener("glft:preview", async (e) => {
                const n = e.detail;
                const r = await httpGet(`${API.read}?path=${encodeURIComponent(n.path)}`);
                if (!r.ok)
                    return alert(r.error || "预览失败");
                const dlg = document.createElement("dialog");
                const ta = document.createElement("textarea");
                ta.style.width = "600px";
                ta.style.height = "360px";
                ta.value = r.content || "";
                dlg.appendChild(ta);
                const btn = document.createElement("button");
                btn.textContent = "关闭";
                btn.onclick = () => dlg.close();
                dlg.appendChild(btn);
                document.body.appendChild(dlg);
                dlg.showModal();
            });
            // 删除
            window.addEventListener("glft:delete", async (e) => {
                var _a;
                const n = e.detail;
                if (!confirm(`确定删除：${n.path} ？`))
                    return;
                const r = await httpPost(API.del, { path: n.path });
                if (!r.ok)
                    return alert(r.error || "删除失败");
                currentPaths = currentPaths.filter((p) => p !== n.path);
                selected.render(currentPaths);
                if (filesWidget) {
                    filesWidget.value = JSON.stringify(currentPaths);
                    (_a = filesWidget.callback) === null || _a === void 0 ? void 0 : _a.call(filesWidget, filesWidget.value);
                }
                renderTree();
            });
            // 新建
            window.addEventListener("glft:new", async (e) => {
                var _a;
                const n = e.detail; // dir
                const name = (_a = prompt("输入文件名（如 my.txt）：", "new.txt")) === null || _a === void 0 ? void 0 : _a.trim();
                if (!name)
                    return;
                const path = (n.path === "." ? name : `${n.path}/${name}`).replace(/\/+/g, "/");
                const r = await httpPost(API.write, { path, content: "" });
                if (!r.ok)
                    return alert(r.error || "创建失败");
                renderTree();
            });
            // 在资源管理器打开（通过 ComfyUI 静态映射无法直接打开；这里提供路径提示）
            window.addEventListener("glft:reveal", (e) => {
                const n = e.detail;
                alert(`请在系统中打开：models/loras_txt/${n.path.replace(/^\.\//, "")}`);
            });
        };
        // ------- 一些简单的样式 -------
        const css = `
    .glft-panel{display:flex;gap:8px;margin:6px 0;}
    .glft-left,.glft-right{flex:1;min-width:240px;border:1px solid var(--border-color);
      border-radius:6px;overflow:hidden}
    .glft-head{padding:6px 8px;font-weight:600;border-bottom:1px solid var(--border-color);
      background:var(--comfy-input-bg)}
    .glft-treebox{max-height:280px;overflow:auto;padding:6px}
    .glft-node{margin-left:8px}
    .glft-title{cursor:pointer;padding:2px 4px;border-radius:4px}
    .glft-title:hover{background:rgba(127,127,127,.15)}
    .glft-children{margin-left:10px;display:none}
    .glft-open>.glft-children{display:block}
    .glft-selected{padding:4px 6px}
    .glft-selected-list{display:flex;flex-direction:column;gap:4px;max-height:280px;overflow:auto}
    .glft-row{display:flex;align-items:center;gap:6px;background:rgba(127,127,127,.08);
      padding:4px;border-radius:4px}
    .glft-row-name{flex:1;font-family:monospace;word-break:break-all}
    .glft-row-btns button{margin-left:4px}
    .glft-menu{position:fixed;z-index:99999;background:var(--comfy-menu-bg);
      border:1px solid var(--border-color);border-radius:6px;overflow:hidden}
    .glft-menu-item{padding:6px 10px;cursor:pointer;white-space:nowrap}
    .glft-menu-item:hover{background:var(--border-color)}
    `;
        const style = document.createElement("style");
        style.textContent = css;
        document.head.appendChild(style);
    },
});
