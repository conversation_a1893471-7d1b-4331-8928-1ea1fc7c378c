"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const base_node_mode_changer_js_1 = require("./base_node_mode_changer.js");
const constants_js_1 = require("./constants.js");
const MODE_MUTE = 2;
const MODE_ALWAYS = 0;
class MuterNode extends base_node_mode_changer_js_1.BaseNodeModeChanger {
    constructor(title = MuterNode.title) {
        super(title);
        this.comfyClass = constants_js_1.NodeTypesString.FAST_MUTER;
        this.modeOn = MODE_ALWAYS;
        this.modeOff = MODE_MUTE;
        this.onConstructed();
    }
    async handleAction(action) {
        if (action === "Mute all") {
            for (const widget of this.widgets) {
                this.forceWidgetOff(widget, true);
            }
        }
        else if (action === "Enable all") {
            for (const widget of this.widgets) {
                this.forceWidgetOn(widget, true);
            }
        }
        else if (action === "Toggle all") {
            for (const widget of this.widgets) {
                this.forceWidgetToggle(widget, true);
            }
        }
    }
}
MuterNode.exposedActions = ["Mute all", "Enable all", "Toggle all"];
MuterNode.type = constants_js_1.NodeTypesString.FAST_MUTER;
MuterNode.title = constants_js_1.NodeTypesString.FAST_MUTER;
app_js_1.app.registerExtension({
    name: "rgthree.Muter",
    registerCustomNodes() {
        MuterNode.setUp();
    },
    loadedGraphNode(node) {
        if (node.type == MuterNode.title) {
            node._tempWidth = node.size[0];
        }
    },
});
