// Get Lora File Text (rgthree) — 纯 JS，无 import 版
(function () {
  const app = window.app;
  const ComfyWidgets = window.ComfyWidgets;

  if (!app || !ComfyWidgets) {
    console.error("[GLFT] app/ComfyWidgets not found. Is ComfyUI fully loaded?");
    return;
  }

  const API = {
    ping: "/rgthree/loras_txt/ping",
    list: "/rgthree/loras_txt/list",
    read: "/rgthree/loras_txt/read",
    write: "/rgthree/loras_txt/write",
    del: "/rgthree/loras_txt/delete",
  };

  async function httpGet(url) {
    const r = await fetch(url, { credentials: "include" });
    return r.json().catch(() => ({}));
  }
  async function httpPost(url, data) {
    const r = await fetch(url, {
      method: "POST",
      credentials: "include",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data || {}),
    });
    return r.json().catch(() => ({}));
  }

  function buildTreeHTML(tree, onPick, onCtx) {
    const root = document.createElement("div");
    root.className = "glft-tree";
    function make(n) {
      const box = document.createElement("div");
      box.className = "glft-node glft-" + n.type;
      const t = document.createElement("div");
      t.className = "glft-title";
      t.textContent = n.name;
      t.onclick = () => {
        if (n.type === "file") onPick(n);
        box.classList.toggle("glft-open");
      };
      t.oncontextmenu = (e) => {
        e.preventDefault();
        onCtx(e.clientX, e.clientY, n);
      };
      box.appendChild(t);
      if (n.children && n.children.length) {
        const kids = document.createElement("div");
        kids.className = "glft-children";
        n.children.forEach((c) => kids.appendChild(make(c)));
        box.appendChild(kids);
      }
      return box;
    }
    root.appendChild(make(tree));
    return root;
  }

  function showMenu(x, y, node, scope) {
    const id = "glft-menu";
    document.getElementById(id)?.remove();
    const m = document.createElement("div");
    m.id = id; m.className = "glft-menu";
    const items = [];
    if (scope === "tree") {
      items.push(["刷新", () => window.dispatchEvent(new CustomEvent("glft:refresh"))]);
      if (node.type === "file") {
        items.push(["预览", () => window.dispatchEvent(new CustomEvent("glft:preview", { detail: node }))]);
        items.push(["删除", () => window.dispatchEvent(new CustomEvent("glft:delete", { detail: node }))]);
      }
      if (node.type === "dir") {
        items.push(["新增 txt", () => window.dispatchEvent(new CustomEvent("glft:new", { detail: node }))]);
      }
      items.push(["在资源管理器中打开", () => window.dispatchEvent(new CustomEvent("glft:reveal", { detail: node }))]);
    } else {
      items.push(["上移", () => window.dispatchEvent(new CustomEvent("glft:sel-up", { detail: node }))]);
      items.push(["下移", () => window.dispatchEvent(new CustomEvent("glft:sel-down", { detail: node }))]);
      items.push(["移除", () => window.dispatchEvent(new CustomEvent("glft:sel-remove", { detail: node }))]);
      items.push(["预览", () => window.dispatchEvent(new CustomEvent("glft:preview", { detail: node }))]);
    }
    items.forEach(([txt, cb]) => {
      const it = document.createElement("div");
      it.className = "glft-menu-item";
      it.textContent = txt;
      it.onclick = () => { cb(); m.remove(); };
      m.appendChild(it);
    });
    m.style.left = x + "px";
    m.style.top = y + "px";
    document.body.appendChild(m);
    document.addEventListener("click", () => m.remove(), { once: true });
  }

  function createSelected(container, getAll, setAll) {
    const wrap = document.createElement("div");
    wrap.className = "glft-selected";
    const head = document.createElement("div");
    head.className = "glft-head";
    head.textContent = "已选择（可排序；输出顺序=此处顺序）";
    const add = document.createElement("button");
    add.textContent = "+ Add Text";
    add.style.float = "right";
    add.onclick = () => alert("在左侧树中点击 .txt 即可添加到列表。");
    head.appendChild(add);

    const list = document.createElement("div");
    list.className = "glft-selected-list";
    wrap.appendChild(head);
    wrap.appendChild(list);
    container.appendChild(wrap);

    function render(items) {
      list.innerHTML = "";
      items.forEach((p, i) => {
        const row = document.createElement("div");
        row.className = "glft-row";
        const name = document.createElement("div");
        name.className = "glft-row-name";
        name.textContent = p;
        name.oncontextmenu = (e) => { e.preventDefault(); showMenu(e.clientX, e.clientY, { index: i, path: p }, "sel"); };
        const btns = document.createElement("div");
        btns.className = "glft-row-btns";
        const up = document.createElement("button"); up.textContent = "↑";
        const dn = document.createElement("button"); dn.textContent = "↓";
        const rm = document.createElement("button"); rm.textContent = "✕";
        up.onclick = () => { if (i>0){ const a=items[i-1]; items[i-1]=items[i]; items[i]=a; setAll(items.slice()); render(items); } };
        dn.onclick = () => { if (i<items.length-1){ const a=items[i+1]; items[i+1]=items[i]; items[i]=a; setAll(items.slice()); render(items); } };
        rm.onclick = () => { items.splice(i,1); setAll(items.slice()); render(items); };
        btns.appendChild(up); btns.appendChild(dn); btns.appendChild(rm);
        row.appendChild(name); row.appendChild(btns);
        list.appendChild(row);
      });
    }
    return { render };
  }

  app.registerExtension({
    name: "rgthree.get_lora_file_text",
    async beforeRegisterNodeDef(nodeDef) {
      const want = "Get Lora File Text";
      if (!(nodeDef?.name && String(nodeDef.name).includes(want))) return;

      const orig = nodeDef.prototype.onNodeCreated;
      nodeDef.prototype.onNodeCreated = function (...args) {
        orig?.apply(this, args);

        // 找到隐藏 files、可见 prefix_text
        let wFiles = this.widgets?.find(w => w.name === "files");
        if (!wFiles) {
          ComfyWidgets.STRING(this, "files", ["STRING", { multiline: false }], app);
          wFiles = this.widgets?.find(w => w.name === "files");
        }
        if (wFiles) wFiles.hidden = true;
        const wPrefix = this.widgets?.find(w => w.name === "prefix_text");
        if (wPrefix) wPrefix.label = "前缀文本（会拼接在最前面）";

        // 自定义 UI：左树+右列表
        const panel = document.createElement("div"); panel.className = "glft-panel";
        const left = document.createElement("div"); left.className = "glft-left";
        const right = document.createElement("div"); right.className = "glft-right";
        const headL = document.createElement("div"); headL.className = "glft-head"; headL.textContent = "models/loras_txt 树形（右键操作）";
        const treeBox = document.createElement("div"); treeBox.className = "glft-treebox";
        left.appendChild(headL); left.appendChild(treeBox);
        panel.appendChild(left); panel.appendChild(right);
        (this.addDOMWidget?.(panel)) || (this.dom?.content?.appendChild(panel));

        let current = [];
        try { current = JSON.parse(wFiles?.value ?? "[]"); } catch { current = []; }
        const setAll = (paths) => { current = paths; if (wFiles){ wFiles.value = JSON.stringify(paths); wFiles.callback?.(wFiles.value);} };
        const getAll = () => current.slice();
        const selected = createSelected(right, getAll, setAll);
        selected.render(current);

        async function renderTree() {
          const r = await httpGet(API.list);
          treeBox.innerHTML = "";
          if (!r || r.ok === false) {
            const err = document.createElement("div");
            err.style.color = "var(--error-color)"; err.style.padding = "6px";
            err.textContent = "读取 loras_txt 失败: " + (r?.error || "unknown");
            treeBox.appendChild(err); return;
          }
          const el = buildTreeHTML(r.tree, (node) => {
            const paths = getAll();
            if (!paths.includes(node.path)) { paths.push(node.path); setAll(paths); selected.render(paths); }
          }, (x,y,node)=>showMenu(x,y,node,"tree"));
          treeBox.appendChild(el);
        }

        renderTree();

        window.addEventListener("glft:refresh", renderTree);
        window.addEventListener("glft:preview", async (e)=>{
          const n = e.detail;
          const r = await httpGet(`${API.read}?path=${encodeURIComponent(n.path||n)}`);
          if (!r || r.ok === false) return alert(r?.error || "预览失败");
          const dlg = document.createElement("dialog");
          const ta = document.createElement("textarea");
          ta.style.width="600px"; ta.style.height="360px"; ta.value = r.content || "";
          const btn = document.createElement("button"); btn.textContent="关闭"; btn.onclick=()=>dlg.close();
          dlg.appendChild(ta); dlg.appendChild(btn); document.body.appendChild(dlg); dlg.showModal();
        });
        window.addEventListener("glft:delete", async (e)=>{
          const n = e.detail; if (!confirm(`确定删除：${n.path} ？`)) return;
          const r = await httpPost(API.del, {path:n.path}); if (!r || r.ok === false) return alert(r?.error||"删除失败");
          const paths = getAll().filter(p=>p!==n.path); setAll(paths); selected.render(paths); renderTree();
        });
        window.addEventListener("glft:new", async (e)=>{
          const n = e.detail;
          let name = prompt("输入文件名（如 my.txt）：", "new.txt"); if (!name) return;
          name = name.trim(); if (!name.toLowerCase().endsWith(".txt")) name += ".txt";
          const path = (n.path==="."? name : `${n.path}/${name}`).replace(/\/+/g,"/");
          const r = await httpPost(API.write, {path, content:""}); if (!r || r.ok === false) return alert(r?.error||"创建失败");
          renderTree();
        });
        window.addEventListener("glft:reveal", (e)=>{
          const n = e.detail;
          alert(`请在系统中打开：models/loras_txt/${(n.path||"").replace(/^\\.\//,"")}`);
        });

        window.addEventListener("glft:sel-up", (e)=>{
          const {index} = e.detail; const paths = getAll();
          if (index>0){ [paths[index-1],paths[index]]=[paths[index],paths[index-1]]; setAll(paths); selected.render(paths); }
        });
        window.addEventListener("glft:sel-down", (e)=>{
          const {index} = e.detail; const paths = getAll();
          if (index<paths.length-1){ [paths[index+1],paths[index]]=[paths[index],paths[index+1]]; setAll(paths); selected.render(paths); }
        });
        window.addEventListener("glft:sel-remove", (e)=>{
          const {index} = e.detail; const paths = getAll();
          paths.splice(index,1); setAll(paths); selected.render(paths);
        });

        // 样式
        const css = `
          .glft-panel{display:flex;gap:8px;margin:6px 0;}
          .glft-left,.glft-right{flex:1;min-width:260px;border:1px solid var(--border-color);
            border-radius:6px;overflow:hidden}
          .glft-head{padding:6px 8px;font-weight:600;border-bottom:1px solid var(--border-color);
            background:var(--comfy-input-bg)}
          .glft-treebox{max-height:280px;overflow:auto;padding:6px}
          .glft-node{margin-left:8px}
          .glft-title{cursor:pointer;padding:2px 4px;border-radius:4px}
          .glft-title:hover{background:rgba(127,127,127,.15)}
          .glft-children{margin-left:10px;display:none}
          .glft-open>.glft-children{display:block}
          .glft-selected{padding:4px 6px}
          .glft-selected-list{display:flex;flex-direction:column;gap:4px;max-height:280px;overflow:auto}
          .glft-row{display:flex;align-items:center;gap:6px;background:rgba(127,127,127,.08);padding:4px;border-radius:4px}
          .glft-row-name{flex:1;font-family:monospace;word-break:break-all}
          .glft-row-btns button{margin-left:4px}
          .glft-menu{position:fixed;z-index:99999;background:var(--comfy-menu-bg);
            border:1px solid var(--border-color);border-radius:6px;overflow:hidden}
          .glft-menu-item{padding:6px 10px;cursor:pointer;white-space:nowrap}
          .glft-menu-item:hover{background:var(--border-color)}
        `;
        const style = document.createElement("style"); style.textContent = css; document.head.appendChild(style);
      };
    },
  });
})();
