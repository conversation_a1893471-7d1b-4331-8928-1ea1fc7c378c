"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.showLoraChooser = showLoraChooser;
exports.showNodesChooser = showNodesChooser;
exports.showWidgetsChooser = showWidgetsChooser;
const app_js_1 = require("scripts/app.js");
const rgthree_api_js_1 = require("rgthree/common/rgthree_api.js");
const PASS_THROUGH = function (item) {
    return item;
};
async function showLoraChooser(event, callback, parentMenu, loras) {
    var _a, _b;
    const canvas = app_js_1.app.canvas;
    if (!loras) {
        loras = ["None", ...(await rgthree_api_js_1.rgthreeApi.getLoras().then((loras) => loras.map((l) => l.file)))];
    }
    const buildMenu = (loras) => {
        const menu = {};
        for (const lora of loras) {
            if (!lora)
                continue;
            const parts = lora.split('/');
            let current = menu;
            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                if (i === parts.length - 1) {
                    current[part] = lora;
                }
                else {
                    current[part] = current[part] || {};
                    current = current[part];
                }
            }
        }
        const finalMenu = [];
        const build = (menu, finalMenu) => {
            for (const key in menu) {
                if (typeof menu[key] === 'string') {
                    finalMenu.push(menu[key]);
                }
                else {
                    const subMenu = [];
                    build(menu[key], subMenu);
                    finalMenu.push({
                        content: key,
                        submenu: {
                            options: subMenu
                        }
                    });
                }
            }
        };
        build(menu, finalMenu);
        return finalMenu;
    };
    const menuItems = buildMenu(loras);
    new LiteGraph.ContextMenu(menuItems, {
        event: event,
        parentMenu: parentMenu != null ? parentMenu : undefined,
        title: "Choose a lora",
        scale: Math.max(1, (_b = (_a = canvas.ds) === null || _a === void 0 ? void 0 : _a.scale) !== null && _b !== void 0 ? _b : 1),
        className: "dark",
        callback: (value) => {
            if (value && value.content) {
                callback(value.content);
            }
            else {
                callback(value);
            }
        },
    });
}
function showNodesChooser(event, mapFn, callback, parentMenu) {
    var _a, _b;
    const canvas = app_js_1.app.canvas;
    const nodesOptions = app_js_1.app.graph._nodes
        .map(mapFn)
        .filter((e) => e != null);
    nodesOptions.sort((a, b) => {
        return a.value - b.value;
    });
    new LiteGraph.ContextMenu(nodesOptions, {
        event: event,
        parentMenu,
        title: "Choose a node id",
        scale: Math.max(1, (_b = (_a = canvas.ds) === null || _a === void 0 ? void 0 : _a.scale) !== null && _b !== void 0 ? _b : 1),
        className: "dark",
        callback,
    });
}
function showWidgetsChooser(event, node, mapFn, callback, parentMenu) {
    var _a, _b;
    const options = (node.widgets || [])
        .map(mapFn)
        .filter((e) => e != null);
    if (options.length) {
        const canvas = app_js_1.app.canvas;
        new LiteGraph.ContextMenu(options, {
            event,
            parentMenu,
            title: "Choose an input/widget",
            scale: Math.max(1, (_b = (_a = canvas.ds) === null || _a === void 0 ? void 0 : _a.scale) !== null && _b !== void 0 ? _b : 1),
            className: "dark",
            callback,
        });
    }
}
