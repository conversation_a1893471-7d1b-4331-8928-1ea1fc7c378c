"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const dynamic_context_base_js_1 = require("./dynamic_context_base.js");
const constants_js_1 = require("./constants.js");
const context_service_js_1 = require("./services/context_service.js");
const utils_js_1 = require("./utils.js");
const shared_utils_js_1 = require("rgthree/common/shared_utils.js");
const utils_canvas_js_1 = require("./utils_canvas.js");
const config_service_js_1 = require("./services/config_service.js");
class DynamicContextSwitchNode extends dynamic_context_base_js_1.DynamicContextNodeBase {
    constructor(title = DynamicContextSwitchNode.title) {
        super(title);
        this.hasShadowInputs = true;
        this.lastInputsList = [];
        this.shadowInputs = [
            { name: "base_ctx", type: "RGTHREE_DYNAMIC_CONTEXT", link: null, count: 0, boundingRect: null },
        ];
    }
    getContextInputsList() {
        return this.shadowInputs;
    }
    handleUpstreamMutation(mutation) {
        this.scheduleHardRefresh();
    }
    onConnectionsChange(type, slotIndex, isConnected, link, inputOrOutput) {
        var _a;
        (_a = super.onConnectionsChange) === null || _a === void 0 ? void 0 : _a.call(this, type, slotIndex, isConnected, link, inputOrOutput);
        if (this.configuring) {
            return;
        }
        if (type === LiteGraph.INPUT) {
            this.scheduleHardRefresh();
        }
    }
    scheduleHardRefresh(ms = 64) {
        return (0, shared_utils_js_1.debounce)(() => {
            this.refreshInputsAndOutputs();
        }, ms);
    }
    onNodeCreated() {
        this.addInput("ctx_1", "RGTHREE_DYNAMIC_CONTEXT");
        this.addInput("ctx_2", "RGTHREE_DYNAMIC_CONTEXT");
        this.addInput("ctx_3", "RGTHREE_DYNAMIC_CONTEXT");
        this.addInput("ctx_4", "RGTHREE_DYNAMIC_CONTEXT");
        this.addInput("ctx_5", "RGTHREE_DYNAMIC_CONTEXT");
        super.onNodeCreated();
    }
    addContextInput(name, type, slot) { }
    refreshInputsAndOutputs() {
        var _a;
        const inputs = [
            { name: "base_ctx", type: "RGTHREE_DYNAMIC_CONTEXT", link: null, count: 0, boundingRect: null },
        ];
        let numConnected = 0;
        for (let i = 0; i < this.inputs.length; i++) {
            const childCtxs = (0, utils_js_1.getConnectedInputNodesAndFilterPassThroughs)(this, this, i);
            if (childCtxs.length > 1) {
                throw new Error("How is there more than one input?");
            }
            const ctx = childCtxs[0];
            if (!ctx)
                continue;
            numConnected++;
            const slotsData = context_service_js_1.SERVICE.getDynamicContextInputsData(ctx);
            console.log(slotsData);
            for (const slotData of slotsData) {
                const found = inputs.find((n) => (0, context_service_js_1.getContextOutputName)(slotData.name) === (0, context_service_js_1.getContextOutputName)(n.name));
                if (found) {
                    found.count += 1;
                    continue;
                }
                inputs.push({
                    name: slotData.name,
                    type: slotData.type,
                    link: null,
                    count: 1,
                    boundingRect: null,
                });
            }
        }
        this.shadowInputs = inputs;
        let i = 0;
        for (i; i < this.shadowInputs.length; i++) {
            const data = this.shadowInputs[i];
            let existing = this.outputs.find((o) => (0, context_service_js_1.getContextOutputName)(o.name) === (0, context_service_js_1.getContextOutputName)(data.name));
            if (!existing) {
                existing = this.addOutput((0, context_service_js_1.getContextOutputName)(data.name), data.type);
            }
            (0, shared_utils_js_1.moveArrayItem)(this.outputs, existing, i);
            delete existing.rgthree_status;
            if (data.count !== numConnected) {
                existing.rgthree_status = "WARN";
            }
        }
        while (this.outputs[i]) {
            const output = this.outputs[i];
            if ((_a = output === null || output === void 0 ? void 0 : output.links) === null || _a === void 0 ? void 0 : _a.length) {
                output.rgthree_status = "ERROR";
                i++;
            }
            else {
                this.removeOutput(i);
            }
        }
        this.fixInputsOutputsLinkSlots();
    }
    onDrawForeground(ctx, canvas) {
        var _a, _b;
        const low_quality = ((_b = (_a = canvas === null || canvas === void 0 ? void 0 : canvas.ds) === null || _a === void 0 ? void 0 : _a.scale) !== null && _b !== void 0 ? _b : 1) < 0.6;
        if (low_quality || this.size[0] <= 10) {
            return;
        }
        let y = LiteGraph.NODE_SLOT_HEIGHT - 1;
        const w = this.size[0];
        ctx.save();
        ctx.font = "normal " + LiteGraph.NODE_SUBTEXT_SIZE + "px Arial";
        ctx.textAlign = "right";
        for (const output of this.outputs) {
            if (!output.rgthree_status) {
                y += LiteGraph.NODE_SLOT_HEIGHT;
                continue;
            }
            const x = w - 20 - (0, utils_canvas_js_1.measureText)(ctx, output.name);
            if (output.rgthree_status === "ERROR") {
                ctx.fillText("🛑", x, y);
            }
            else if (output.rgthree_status === "WARN") {
                ctx.fillText("⚠️", x, y);
            }
            y += LiteGraph.NODE_SLOT_HEIGHT;
        }
        ctx.restore();
    }
}
DynamicContextSwitchNode.title = constants_js_1.NodeTypesString.DYNAMIC_CONTEXT_SWITCH;
DynamicContextSwitchNode.type = constants_js_1.NodeTypesString.DYNAMIC_CONTEXT_SWITCH;
DynamicContextSwitchNode.comfyClass = constants_js_1.NodeTypesString.DYNAMIC_CONTEXT_SWITCH;
app_js_1.app.registerExtension({
    name: "rgthree.DynamicContextSwitch",
    async beforeRegisterNodeDef(nodeType, nodeData) {
        if (!config_service_js_1.SERVICE.getConfigValue("unreleased.dynamic_context.enabled")) {
            return;
        }
        if (nodeData.name === DynamicContextSwitchNode.type) {
            DynamicContextSwitchNode.setUp(nodeType, nodeData);
        }
    },
});
