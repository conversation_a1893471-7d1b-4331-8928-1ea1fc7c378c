// Get Lora File Text (rgthree) — 纯 JS 前端扩展
// 动态添加/删除（+ Add Text 按钮、右键菜单），树形多级目录，只输出 TEXT。

/* eslint-disable no-undef */
// @ts-ignore
import { app } from "../../scripts/app.js";
// @ts-ignore
import { ComfyWidgets } from "../../scripts/widgets.js";

const API = {
  list: "/rgthree/loras_txt/list",
  read: "/rgthree/loras_txt/read",
  write: "/rgthree/loras_txt/write",
  del: "/rgthree/loras_txt/delete",
};

async function httpGet(url) {
  const r = await fetch(url, { credentials: "include" });
  if (!r.ok) throw new Error(await r.text());
  return await r.json();
}
async function httpPost(url, data) {
  const r = await fetch(url, {
    method: "POST",
    credentials: "include",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data || {}),
  });
  if (!r.ok) throw new Error(await r.text());
  return await r.json();
}

// 简易树
function buildTreeHTML(tree, onPick, onContext) {
  const root = document.createElement("div");
  root.className = "glft-tree";
  function makeNode(n) {
    const li = document.createElement("div");
    li.className = `glft-node glft-${n.type}`;
    const title = document.createElement("div");
    title.className = "glft-title";
    title.textContent = n.name;
    title.onclick = () => {
      if (n.type === "file") onPick(n);
      li.classList.toggle("glft-open");
    };
    title.oncontextmenu = (e) => {
      e.preventDefault();
      onContext(e.clientX, e.clientY, n);
    };
    li.appendChild(title);
    if (n.children && n.children.length) {
      const box = document.createElement("div");
      box.className = "glft-children";
      n.children.forEach((c) => box.appendChild(makeNode(c)));
      li.appendChild(box);
    }
    return li;
  }
  root.appendChild(makeNode(tree));
  return root;
}

function showMenu(x, y, node, scope) {
  const id = "glft-menu";
  document.getElementById(id)?.remove();
  const menu = document.createElement("div");
  menu.id = id;
  menu.className = "glft-menu";
  const items = [];

  if (scope === "tree") {
    items.push(["刷新", () => window.dispatchEvent(new CustomEvent("glft:refresh"))]);
    if (node.type === "file") {
      items.push(["预览", () => window.dispatchEvent(new CustomEvent("glft:preview", { detail: node }))]);
      items.push(["删除", () => window.dispatchEvent(new CustomEvent("glft:delete", { detail: node }))]);
    }
    if (node.type === "dir") {
      items.push(["新增 txt", () => window.dispatchEvent(new CustomEvent("glft:new", { detail: node }))]);
    }
    items.push(["在资源管理器中打开", () => window.dispatchEvent(new CustomEvent("glft:reveal", { detail: node }))]);
  } else if (scope === "selected") {
    items.push(["上移", () => window.dispatchEvent(new CustomEvent("glft:sel-move-up", { detail: node }))]);
    items.push(["下移", () => window.dispatchEvent(new CustomEvent("glft:sel-move-down", { detail: node }))]);
    items.push(["移除", () => window.dispatchEvent(new CustomEvent("glft:sel-remove", { detail: node }))]);
    items.push(["预览", () => window.dispatchEvent(new CustomEvent("glft:preview", { detail: node }))]);
  }

  items.forEach(([text, cb]) => {
    const it = document.createElement("div");
    it.className = "glft-menu-item";
    it.textContent = text;
    it.onclick = () => { cb(); menu.remove(); };
    menu.appendChild(it);
  });

  menu.style.left = x + "px";
  menu.style.top = y + "px";
  document.body.appendChild(menu);
  document.addEventListener("click", () => menu.remove(), { once: true });
}

function createSelectedList(container, getAllPaths, setAllPaths) {
  const wrap = document.createElement("div");
  wrap.className = "glft-selected";
  const header = document.createElement("div");
  header.className = "glft-sel-header";
  const add = document.createElement("button");
  add.textContent = "+ Add Text";
  add.title = "从左侧树形中选择条目后，这里也可再添加一个空位（会弹出选择器）";
  header.appendChild(add);
  const list = document.createElement("div");
  list.className = "glft-selected-list";
  wrap.appendChild(header);
  wrap.appendChild(list);
  container.appendChild(wrap);

  function render(items) {
    list.innerHTML = "";
    items.forEach((p, i) => {
      const row = document.createElement("div");
      row.className = "glft-row";
      const name = document.createElement("div");
      name.className = "glft-row-name";
      name.textContent = p;
      name.oncontextmenu = (e) => { e.preventDefault(); showMenu(e.clientX, e.clientY, { index:i, path:p }, "selected"); };

      const btns = document.createElement("div");
      btns.className = "glft-row-btns";
      const up = document.createElement("button"); up.textContent = "↑"; up.title = "上移";
      up.onclick = () => { if (i>0) { [items[i-1],items[i]]=[items[i],items[i-1]]; setAllPaths(items.slice()); render(items); } };
      const down = document.createElement("button"); down.textContent = "↓"; down.title = "下移";
      down.onclick = () => { if (i<items.length-1) { [items[i+1],items[i]]=[items[i],items[i+1]]; setAllPaths(items.slice()); render(items); } };
      const del = document.createElement("button"); del.textContent = "✕"; del.title = "移除";
      del.onclick = () => { items.splice(i,1); setAllPaths(items.slice()); render(items); };
      btns.appendChild(up); btns.appendChild(down); btns.appendChild(del);

      row.appendChild(name); row.appendChild(btns);
      list.appendChild(row);
    });
  }

  add.onclick = () => {
    // 引导用户到左侧树中点击选择；这里只做提示
    alert("在左侧树形列表里点击一个 .txt 文件来添加。");
  };

  return { render };
}

app.registerExtension({
  name: "rgthree.get_lora_file_text",
  async beforeRegisterNodeDef(nodeDef) {
    // 宽松匹配：避免显示名微调导致前端挂不上
    const wanted = "Get Lora File Text";
    const isTarget = (n) => typeof n === "string" && n.includes(wanted);
    if (!isTarget(nodeDef?.name)) return;

    const orig = nodeDef.prototype.onNodeCreated;
    nodeDef.prototype.onNodeCreated = function(...args){
      orig?.apply(this, args);

      // 找到隐藏 files / 可见 prefix_text
      let wFiles = this.widgets?.find(w => w.name === "files");
      if (!wFiles) {
        ComfyWidgets.STRING(this, "files", ["STRING", {multiline:false}], app);
        wFiles = this.widgets?.find(w => w.name === "files");
      }
      if (wFiles) wFiles.hidden = true;

      const wPrefix = this.widgets?.find(w => w.name === "prefix_text");
      if (wPrefix) wPrefix.label = "前缀文本 (会拼接在所有内容最前面)";

      // 自定义面板：左树 + 右侧已选择（含 +Add Text、右键）
      const panel = document.createElement("div"); panel.className = "glft-panel";
      const left = document.createElement("div"); left.className = "glft-left";
      const right = document.createElement("div"); right.className = "glft-right";

      const headL = document.createElement("div"); headL.className="glft-head"; headL.textContent="models/loras_txt 树形 (右键操作)";
      const treeBox = document.createElement("div"); treeBox.className="glft-treebox";
      left.appendChild(headL); left.appendChild(treeBox);

      const headR = document.createElement("div"); headR.className="glft-head"; headR.textContent="已选择（可排序；输出顺序=此处顺序）";
      right.appendChild(headR);

      panel.appendChild(left); panel.appendChild(right);
      (this.addDOMWidget?.(panel)) || (this.dom?.content?.appendChild(panel));

      let current = [];
      try { current = JSON.parse(wFiles?.value ?? "[]"); } catch { current = []; }

      const setAllPaths = (paths) => {
        current = paths;
        if (wFiles) { wFiles.value = JSON.stringify(paths); wFiles.callback?.(wFiles.value); }
      };
      const getAllPaths = () => current.slice();

      const selected = createSelectedList(right, getAllPaths, setAllPaths);
      selected.render(current);

      async function renderTree(){
        const data = await httpGet(API.list);
        treeBox.innerHTML = "";
        const el = buildTreeHTML(
          data,
          (node)=>{ // 点击文件添加到右侧
            const paths = getAllPaths();
            if (!paths.includes(node.path)) {
              paths.push(node.path);
              setAllPaths(paths);
              selected.render(paths);
            }
          },
          (x,y,node)=>showMenu(x,y,node,"tree")
        );
        treeBox.appendChild(el);
      }

      renderTree();

      // —— 事件绑定 —— //
      window.addEventListener("glft:refresh", renderTree);
      window.addEventListener("glft:preview", async (e)=>{
        const n = e.detail;
        const r = await httpGet(`${API.read}?path=${encodeURIComponent(n.path||n)}`);
        if (!r.ok) return alert(r.error || "预览失败");
        const dlg = document.createElement("dialog");
        const ta = document.createElement("textarea");
        ta.style.width="600px"; ta.style.height="360px"; ta.value = r.content || "";
        const btn = document.createElement("button"); btn.textContent="关闭"; btn.onclick=()=>dlg.close();
        dlg.appendChild(ta); dlg.appendChild(btn); document.body.appendChild(dlg); dlg.showModal();
      });
      window.addEventListener("glft:delete", async (e)=>{
        const n = e.detail; if (!confirm(`确定删除：${n.path} ？`)) return;
        const r = await httpPost(API.del, {path:n.path}); if (!r.ok) return alert(r.error||"删除失败");
        const paths = getAllPaths().filter(p=>p!==n.path);
        setAllPaths(paths); selected.render(paths); renderTree();
      });
      window.addEventListener("glft:new", async (e)=>{
        const n = e.detail;
        let name = prompt("输入文件名（如 my.txt）：", "new.txt"); if (!name) return;
        name = name.trim(); if (!name.toLowerCase().endsWith(".txt")) name += ".txt";
        const path = (n.path==="."? name : `${n.path}/${name}`).replace(/\/+/g,"/");
        const r = await httpPost(API.write, {path, content:""}); if (!r.ok) return alert(r.error||"创建失败");
        renderTree();
      });
      window.addEventListener("glft:reveal", (e)=>{
        const n = e.detail; alert(`请在系统中打开：models/loras_txt/${(n.path||"").replace(/^\\.\//,"")}`);
      });

      // 右侧所选：右键上移/下移/移除
      window.addEventListener("glft:sel-move-up", (e)=>{
        const {index} = e.detail; const paths = getAllPaths();
        if (index>0){ [paths[index-1],paths[index]]=[paths[index],paths[index-1]]; setAllPaths(paths); selected.render(paths); }
      });
      window.addEventListener("glft:sel-move-down", (e)=>{
        const {index} = e.detail; const paths = getAllPaths();
        if (index<paths.length-1){ [paths[index+1],paths[index]]=[paths[index],paths[index+1]]; setAllPaths(paths); selected.render(paths); }
      });
      window.addEventListener("glft:sel-remove", (e)=>{
        const {index} = e.detail; const paths = getAllPaths();
        paths.splice(index,1); setAllPaths(paths); selected.render(paths);
      });
    };

    // 样式
    const css = `
    .glft-panel{display:flex;gap:8px;margin:6px 0;}
    .glft-left,.glft-right{flex:1;min-width:260px;border:1px solid var(--border-color);
      border-radius:6px;overflow:hidden}
    .glft-head{padding:6px 8px;font-weight:600;border-bottom:1px solid var(--border-color);
      background:var(--comfy-input-bg)}
    .glft-treebox{max-height:280px;overflow:auto;padding:6px}
    .glft-node{margin-left:8px}
    .glft-title{cursor:pointer;padding:2px 4px;border-radius:4px}
    .glft-title:hover{background:rgba(127,127,127,.15)}
    .glft-children{margin-left:10px;display:none}
    .glft-open>.glft-children{display:block}
    .glft-selected{padding:4px 6px}
    .glft-sel-header{display:flex;justify-content:flex-end;padding:4px 0}
    .glft-selected-list{display:flex;flex-direction:column;gap:4px;max-height:280px;overflow:auto}
    .glft-row{display:flex;align-items:center;gap:6px;background:rgba(127,127,127,.08);
      padding:4px;border-radius:4px}
    .glft-row-name{flex:1;font-family:monospace;word-break:break-all}
    .glft-row-btns button{margin-left:4px}
    .glft-menu{position:fixed;z-index:99999;background:var(--comfy-menu-bg);
      border:1px solid var(--border-color);border-radius:6px;overflow:hidden}
    .glft-menu-item{padding:6px 10px;cursor:pointer;white-space:nowrap}
    .glft-menu-item:hover{background:var(--border-color)}
    `;
    const style = document.createElement("style");
    style.textContent = css;
    document.head.appendChild(style);
  },
});
