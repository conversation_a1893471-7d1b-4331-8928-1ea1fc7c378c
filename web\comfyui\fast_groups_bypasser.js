"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FastGroupsBypasser = void 0;
const app_js_1 = require("scripts/app.js");
const constants_js_1 = require("./constants.js");
const fast_groups_muter_js_1 = require("./fast_groups_muter.js");
class FastGroupsBypasser extends fast_groups_muter_js_1.BaseFastGroupsModeChanger {
    constructor(title = FastGroupsBypasser.title) {
        super(title);
        this.comfyClass = constants_js_1.NodeTypesString.FAST_GROUPS_BYPASSER;
        this.helpActions = "bypass and enable";
        this.modeOn = LiteGraph.ALWAYS;
        this.modeOff = 4;
        this.onConstructed();
    }
}
exports.FastGroupsBypasser = FastGroupsBypasser;
FastGroupsBypasser.type = constants_js_1.NodeTypesString.FAST_GROUPS_BYPASSER;
FastGroupsBypasser.title = constants_js_1.NodeTypesString.FAST_GROUPS_BYPASSER;
FastGroupsBypasser.exposedActions = ["Bypass all", "Enable all", "Toggle all"];
app_js_1.app.registerExtension({
    name: "rgthree.FastGroupsBypasser",
    registerCustomNodes() {
        FastGroupsBypasser.setUp();
    },
    loadedGraphNode(node) {
        if (node.type == FastGroupsBypasser.title) {
            node.tempSize = [...node.size];
        }
    },
});
