"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RgthreeModelInfoCard = void 0;
const base_custom_element_1 = require("rgthree/common/components/base_custom_element");
class RgthreeModelInfoCard extends base_custom_element_1.RgthreeCustomElement {
    constructor() {
        super(...arguments);
        this.data = {};
    }
    getModified(value, data, currentElement, contextElement) {
        const date = new Date(value);
        return String(`${date.toLocaleDateString()} ${date.toLocaleTimeString()}`);
    }
    getCivitaiLink(links) {
        return (links === null || links === void 0 ? void 0 : links.find((i) => i.includes("civitai.com/models"))) || null;
    }
    setModelData(data) {
        this.data = data;
    }
    hasBaseModel(baseModel) {
        return this.data.baseModel === baseModel;
    }
    hasData(field) {
        var _a;
        if (field === "civitai") {
            return !!((_a = this.getCivitaiLink(this.data.links)) === null || _a === void 0 ? void 0 : _a.length);
        }
        return !!this.data[field];
    }
    matchesQueryText(query) {
        var _a;
        return (_a = (this.data.name || this.data.file)) === null || _a === void 0 ? void 0 : _a.includes(query);
    }
    hide() {
        this.classList.add("-is-hidden");
    }
    show() {
        this.classList.remove("-is-hidden");
    }
}
exports.RgthreeModelInfoCard = RgthreeModelInfoCard;
RgthreeModelInfoCard.NAME = "rgthree-model-info-card";
RgthreeModelInfoCard.TEMPLATES = "components/model-info-card.html";
RgthreeModelInfoCard.CSS = "components/model-info-card.css";
RgthreeModelInfoCard.USE_SHADOW = false;
