"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const base_node_js_1 = require("./base_node.js");
const constants_js_1 = require("./constants.js");
class ImageInsetCrop extends base_node_js_1.RgthreeBaseServerNode {
    constructor(title = ImageInsetCrop.title) {
        super(title);
    }
    onAdded(graph) {
        const measurementWidget = this.widgets[0];
        let callback = measurementWidget.callback;
        measurementWidget.callback = (...args) => {
            this.setWidgetStep();
            callback && callback.apply(measurementWidget, [...args]);
        };
        this.setWidgetStep();
    }
    configure(info) {
        super.configure(info);
        this.setWidgetStep();
    }
    setWidgetStep() {
        const measurementWidget = this.widgets[0];
        for (let i = 1; i <= 4; i++) {
            if (measurementWidget.value === "Pixels") {
                this.widgets[i].options.step = 80;
                this.widgets[i].options.max = ImageInsetCrop.maxResolution;
            }
            else {
                this.widgets[i].options.step = 10;
                this.widgets[i].options.max = 99;
            }
        }
    }
    async handleAction(action) {
        if (action === "Reset Crop") {
            for (const widget of this.widgets) {
                if (["left", "right", "top", "bottom"].includes(widget.name)) {
                    widget.value = 0;
                }
            }
        }
    }
    static setUp(comfyClass, nodeData) {
        base_node_js_1.RgthreeBaseServerNode.registerForOverride(comfyClass, nodeData, ImageInsetCrop);
    }
}
ImageInsetCrop.title = constants_js_1.NodeTypesString.IMAGE_INSET_CROP;
ImageInsetCrop.type = constants_js_1.NodeTypesString.IMAGE_INSET_CROP;
ImageInsetCrop.comfyClass = constants_js_1.NodeTypesString.IMAGE_INSET_CROP;
ImageInsetCrop.exposedActions = ["Reset Crop"];
ImageInsetCrop.maxResolution = 8192;
app_js_1.app.registerExtension({
    name: "rgthree.ImageInsetCrop",
    async beforeRegisterNodeDef(nodeType, nodeData) {
        if (nodeData.name === constants_js_1.NodeTypesString.IMAGE_INSET_CROP) {
            ImageInsetCrop.setUp(nodeType, nodeData);
        }
    },
});
