"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const base_node_js_1 = require("./base_node.js");
const constants_js_1 = require("./constants.js");
class RgthreeImageOrLatentSize extends base_node_js_1.RgthreeBaseServerNode {
    static setUp(comfyClass, nodeData) {
        base_node_js_1.RgthreeBaseServerNode.registerForOverride(comfyClass, nodeData, NODE_CLASS);
    }
    constructor(title = NODE_CLASS.title) {
        super(title);
    }
    onNodeCreated() {
        var _a;
        (_a = super.onNodeCreated) === null || _a === void 0 ? void 0 : _a.call(this);
        this.addInput("input", ["IMAGE", "LATENT", "MASK"]);
    }
    configure(info) {
        var _a;
        super.configure(info);
        if ((_a = this.inputs) === null || _a === void 0 ? void 0 : _a.length) {
            this.inputs[0].type = ["IMAGE", "LATENT", "MASK"];
        }
    }
}
RgthreeImageOrLatentSize.title = constants_js_1.NodeTypesString.IMAGE_OR_LATENT_SIZE;
RgthreeImageOrLatentSize.type = constants_js_1.NodeTypesString.IMAGE_OR_LATENT_SIZE;
RgthreeImageOrLatentSize.comfyClass = constants_js_1.NodeTypesString.IMAGE_OR_LATENT_SIZE;
const NODE_CLASS = RgthreeImageOrLatentSize;
app_js_1.app.registerExtension({
    name: "rgthree.ImageOrLatentSize",
    async beforeRegisterNodeDef(nodeType, nodeData) {
        if (nodeData.name === NODE_CLASS.type) {
            NODE_CLASS.setUp(nodeType, nodeData);
        }
    },
});
