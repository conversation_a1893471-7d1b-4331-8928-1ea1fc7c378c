"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SERVICE = void 0;
const app_js_1 = require("scripts/app.js");
const constants_js_1 = require("../constants.js");
class BookmarksService {
    getCurrentBookmarks() {
        return app_js_1.app.graph._nodes
            .filter((n) => n.type === constants_js_1.NodeTypesString.BOOKMARK)
            .sort((a, b) => a.title.localeCompare(b.title));
    }
}
exports.SERVICE = new BookmarksService();
