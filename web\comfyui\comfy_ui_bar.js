"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const buttonGroup_js_1 = require("scripts/ui/components/buttonGroup.js");
const button_js_1 = require("scripts/ui/components/button.js");
const svgs_js_1 = require("rgthree/common/media/svgs.js");
const utils_dom_js_1 = require("rgthree/common/utils_dom.js");
const bookmarks_services_js_1 = require("./services/bookmarks_services.js");
const config_service_js_1 = require("./services/config_service.js");
const popup_js_1 = require("scripts/ui/components/popup.js");
const config_js_1 = require("./config.js");
let rgthreeButtonGroup = null;
function addRgthreeTopBarButtons() {
    var _a, _b, _c;
    if (!config_service_js_1.SERVICE.getFeatureValue("comfy_top_bar_menu.enabled")) {
        if ((_a = rgthreeButtonGroup === null || rgthreeButtonGroup === void 0 ? void 0 : rgthreeButtonGroup.element) === null || _a === void 0 ? void 0 : _a.parentElement) {
            rgthreeButtonGroup.element.parentElement.removeChild(rgthreeButtonGroup.element);
        }
        return;
    }
    else if (rgthreeButtonGroup) {
        (_b = app_js_1.app.menu) === null || _b === void 0 ? void 0 : _b.settingsGroup.element.before(rgthreeButtonGroup.element);
        return;
    }
    const buttons = [];
    const rgthreeButton = new button_js_1.ComfyButton({
        icon: "rgthree",
        tooltip: "rgthree-comfy",
        app: app_js_1.app,
        enabled: true,
        classList: "comfyui-button comfyui-menu-mobile-collapse primary",
    });
    buttons.push(rgthreeButton);
    rgthreeButton.iconElement.style.width = "1.2rem";
    rgthreeButton.iconElement.innerHTML = svgs_js_1.logoRgthree;
    rgthreeButton.withPopup(new popup_js_1.ComfyPopup({ target: rgthreeButton.element, classList: "rgthree-top-menu" }, (0, utils_dom_js_1.createElement)("menu", {
        children: [
            (0, utils_dom_js_1.createElement)("li", {
                child: (0, utils_dom_js_1.createElement)("button.rgthree-button-reset", {
                    html: svgs_js_1.iconGear + "Settings (rgthree-comfy)",
                    onclick: () => new config_js_1.RgthreeConfigDialog().show(),
                }),
            }),
            (0, utils_dom_js_1.createElement)("li", {
                child: (0, utils_dom_js_1.createElement)("button.rgthree-button-reset", {
                    html: svgs_js_1.iconStarFilled + "Star on Github",
                    onclick: () => window.open("https://github.com/rgthree/rgthree-comfy", "_blank"),
                }),
            }),
        ],
    })), "click");
    if (config_service_js_1.SERVICE.getFeatureValue("comfy_top_bar_menu.button_bookmarks.enabled")) {
        const bookmarksListEl = (0, utils_dom_js_1.createElement)("menu");
        bookmarksListEl.appendChild((0, utils_dom_js_1.createElement)("li.rgthree-message", {
            child: (0, utils_dom_js_1.createElement)("span", { text: "No bookmarks in current workflow." }),
        }));
        const bookmarksButton = new button_js_1.ComfyButton({
            icon: "bookmark",
            tooltip: "Workflow Bookmarks (rgthree-comfy)",
            app: app_js_1.app,
        });
        const bookmarksPopup = new popup_js_1.ComfyPopup({ target: bookmarksButton.element, classList: "rgthree-top-menu" }, bookmarksListEl);
        bookmarksPopup.addEventListener("open", () => {
            const bookmarks = bookmarks_services_js_1.SERVICE.getCurrentBookmarks();
            (0, utils_dom_js_1.empty)(bookmarksListEl);
            if (bookmarks.length) {
                for (const b of bookmarks) {
                    bookmarksListEl.appendChild((0, utils_dom_js_1.createElement)("li", {
                        child: (0, utils_dom_js_1.createElement)("button.rgthree-button-reset", {
                            text: `[${b.shortcutKey}] ${b.title}`,
                            onclick: () => {
                                b.canvasToBookmark();
                            },
                        }),
                    }));
                }
            }
            else {
                bookmarksListEl.appendChild((0, utils_dom_js_1.createElement)("li.rgthree-message", {
                    child: (0, utils_dom_js_1.createElement)("span", { text: "No bookmarks in current workflow." }),
                }));
            }
            bookmarksPopup.update();
        });
        bookmarksButton.withPopup(bookmarksPopup, "hover");
        buttons.push(bookmarksButton);
    }
    rgthreeButtonGroup = new buttonGroup_js_1.ComfyButtonGroup(...buttons);
    (_c = app_js_1.app.menu) === null || _c === void 0 ? void 0 : _c.settingsGroup.element.before(rgthreeButtonGroup.element);
}
app_js_1.app.registerExtension({
    name: "rgthree.TopMenu",
    async setup() {
        addRgthreeTopBarButtons();
        config_service_js_1.SERVICE.addEventListener("config-change", ((e) => {
            var _a, _b;
            if ((_b = (_a = e.detail) === null || _a === void 0 ? void 0 : _a.key) === null || _b === void 0 ? void 0 : _b.includes("features.comfy_top_bar_menu")) {
                addRgthreeTopBarButtons();
            }
        }));
    },
});
