"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_js_1 = require("scripts/app.js");
const utils_js_1 = require("./utils.js");
const base_node_js_1 = require("./base_node.js");
const constants_js_1 = require("./constants.js");
const utils_inputs_outputs_js_1 = require("./utils_inputs_outputs.js");
const shared_utils_js_1 = require("rgthree/common/shared_utils.js");
class RgthreeAnySwitch extends base_node_js_1.RgthreeBaseServerNode {
    constructor(title = RgthreeAnySwitch.title) {
        super(title);
        this.stabilizeBound = this.stabilize.bind(this);
        this.nodeType = null;
        this.addAnyInput(5);
    }
    onConnectionsChange(type, slotIndex, isConnected, linkInfo, ioSlot) {
        var _a;
        (_a = super.onConnectionsChange) === null || _a === void 0 ? void 0 : _a.call(this, type, slotIndex, isConnected, linkInfo, ioSlot);
        this.scheduleStabilize();
    }
    onConnectionsChainChange() {
        this.scheduleStabilize();
    }
    scheduleStabilize(ms = 64) {
        return (0, shared_utils_js_1.debounce)(this.stabilizeBound, ms);
    }
    addAnyInput(num = 1) {
        for (let i = 0; i < num; i++) {
            this.addInput(`any_${String(this.inputs.length + 1).padStart(2, "0")}`, (this.nodeType || "*"));
        }
    }
    stabilize() {
        (0, utils_inputs_outputs_js_1.removeUnusedInputsFromEnd)(this, 4);
        this.addAnyInput();
        let connectedType = (0, utils_js_1.followConnectionUntilType)(this, utils_js_1.IoDirection.INPUT, undefined, true);
        if (!connectedType) {
            connectedType = (0, utils_js_1.followConnectionUntilType)(this, utils_js_1.IoDirection.OUTPUT, undefined, true);
        }
        this.nodeType = (connectedType === null || connectedType === void 0 ? void 0 : connectedType.type) || "*";
        for (const input of this.inputs) {
            input.type = this.nodeType;
        }
        for (const output of this.outputs) {
            output.type = this.nodeType;
            output.label =
                output.type === "RGTHREE_CONTEXT"
                    ? "CONTEXT"
                    : Array.isArray(this.nodeType) || this.nodeType.includes(",")
                        ? (connectedType === null || connectedType === void 0 ? void 0 : connectedType.label) || String(this.nodeType)
                        : String(this.nodeType);
        }
    }
    static setUp(comfyClass, nodeData) {
        base_node_js_1.RgthreeBaseServerNode.registerForOverride(comfyClass, nodeData, RgthreeAnySwitch);
        (0, utils_js_1.addConnectionLayoutSupport)(RgthreeAnySwitch, app_js_1.app, [
            ["Left", "Right"],
            ["Right", "Left"],
        ]);
    }
}
RgthreeAnySwitch.title = constants_js_1.NodeTypesString.ANY_SWITCH;
RgthreeAnySwitch.type = constants_js_1.NodeTypesString.ANY_SWITCH;
RgthreeAnySwitch.comfyClass = constants_js_1.NodeTypesString.ANY_SWITCH;
app_js_1.app.registerExtension({
    name: "rgthree.AnySwitch",
    async beforeRegisterNodeDef(nodeType, nodeData, app) {
        if (nodeData.name === "Any Switch (rgthree)") {
            RgthreeAnySwitch.setUp(nodeType, nodeData);
        }
    },
});
