# -*- coding: utf-8 -*-
"""
Get Lora File Text (rgthree) — 仅输出 TEXT
------------------------------------------
特性：
1) 只识别 models/loras_txt 下的 .txt（含子目录；树形）
2) HTTP 接口：列出/读取/写入/删除，用于前端右键菜单
3) 节点输入：
   - files:   前端维护的选中的 txt 相对路径 JSON 数组（隐藏）
   - prefix_text: 前缀文本（拼接在所有 txt 内容前）
4) 节点输出：仅 ("STRING",) -> TEXT
"""

from __future__ import annotations
import io
import os
import json
from typing import List, Dict

# 兼容项目里的工具；没有也能跑
try:
    from ..constants import get_category, get_name  # type: ignore
except Exception:
    def get_category():
        return "rgthree/utils"
    def get_name(n: str):
        return f"{n} (rgthree)"

try:
    import folder_paths  # ComfyUI 原生
except Exception:
    class _FP:  # 兜底，防类型报警
        models_dir = os.path.join(os.getcwd(), "models")
        def get_folder_paths(self, *_a, **_k): return [os.path.join(self.models_dir, "loras")]
    folder_paths = _FP()  # type: ignore


# ---------- 路径与工具 ----------
def _base_dir() -> str:
    """models/loras_txt 根目录，确保存在"""
    try:
        loras_dir = folder_paths.get_folder_paths("loras")[0]  # type: ignore
        base = os.path.join(os.path.dirname(loras_dir), "loras_txt")
    except Exception:
        base = os.path.join(getattr(folder_paths, "models_dir", "models"), "loras_txt")
    os.makedirs(base, exist_ok=True)
    return base


def _is_txt(path: str) -> bool:
    return path.lower().endswith(".txt")


def _abs_in_base(rel_path: str) -> str:
    base = os.path.abspath(_base_dir())
    p = os.path.abspath(os.path.join(base, rel_path))
    if not (p == base or p.startswith(base + os.sep)):
        raise RuntimeError("非法路径（越界）：%s" % rel_path)
    return p


def _list_tree() -> Dict:
    """返回树形结构：{name,type,path,children}"""
    root = _base_dir()

    def walk(d: str) -> Dict:
        children = []
        for name in sorted(os.listdir(d)):
            full = os.path.join(d, name)
            rel = os.path.relpath(full, root).replace("\\", "/")
            if os.path.isdir(full):
                item = walk(full)
                item.update({"name": name, "type": "dir", "path": rel})
                children.append(item)
            elif _is_txt(full):
                children.append({"name": name, "type": "file", "path": rel})
        return {"children": children}

    tree = walk(root)
    tree.update({"name": os.path.basename(root) or "loras_txt", "type": "dir", "path": "."})
    return tree


def _read(rel_path: str) -> str:
    p = _abs_in_base(rel_path)
    if not (os.path.isfile(p) and _is_txt(p)):
        raise FileNotFoundError(rel_path)
    with io.open(p, "r", encoding="utf-8", errors="ignore") as f:
        return f.read()


def _write(rel_path: str, content: str) -> None:
    p = _abs_in_base(rel_path)
    os.makedirs(os.path.dirname(p), exist_ok=True)
    with io.open(p, "w", encoding="utf-8") as f:
        f.write(content or "")


def _delete(rel_path: str) -> None:
    p = _abs_in_base(rel_path)
    if os.path.isdir(p):
        os.rmdir(p)  # 仅删除空目录
    elif os.path.isfile(p):
        os.remove(p)


# ---------- HTTP 路由（供前端 TS 调用） ----------
def _register_http():
    try:
        from server import PromptServer  # ComfyUI
    except Exception:
        return
    ps = PromptServer.instance

    @ps.routes.get("/rgthree/loras_txt/list")
    async def _r_list(_req):  # noqa: ANN001
        return ps.json(_list_tree())

    @ps.routes.get("/rgthree/loras_txt/read")
    async def _r_read(req):  # noqa: ANN001
        rel = req.query.get("path", "")
        try:
            return ps.json({"ok": True, "content": _read(rel)})
        except Exception as e:  # noqa: BLE001
            return ps.json({"ok": False, "error": str(e)})

    @ps.routes.post("/rgthree/loras_txt/write")
    async def _r_write(req):  # noqa: ANN001
        data = await req.json()
        try:
            _write(data.get("path", ""), data.get("content", ""))
            return ps.json({"ok": True})
        except Exception as e:  # noqa: BLE001
            return ps.json({"ok": False, "error": str(e)})

    @ps.routes.post("/rgthree/loras_txt/delete")
    async def _r_delete(req):  # noqa: ANN001
        data = await req.json()
        try:
            _delete(data.get("path", ""))
            return ps.json({"ok": True})
        except Exception as e:  # noqa: BLE001
            return ps.json({"ok": False, "error": str(e)})

_register_http()


# ---------- 节点实现：仅输出 TEXT ----------
class GetLoraFileText:
    """
    从 models/loras_txt（含子目录）读取多个 .txt，按顺序合并，
    并在最前拼接 `prefix_text`，只输出一根 STRING -> TEXT。
    """

    NAME = get_name("Get Lora File Text")
    CATEGORY = get_category()

    @classmethod
    def INPUT_TYPES(cls):  # noqa: N802
        # `files` 由前端 TS 隐藏维护（JSON: list[str]）
        return {
            "required": {
                "files": ("STRING", {
                    "default": "[]",
                    "multiline": False,
                    "tooltip": "由前端维护的选中文件相对路径(JSON)",
                }),
                "prefix_text": ("STRING", {
                    "default": "",
                    "multiline": True,
                    "placeholder": "这里输入要拼接在最前面的文本…",
                }),
            },
            "optional": {
                "_refresh": ("BOOLEAN", {"default": False})  # 仅用于触发 UI 刷新，不参与计算
            }
        }

    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("TEXT",)
    FUNCTION = "run"
    OUTPUT_NODE = False

    def run(self, files: str, prefix_text: str, _refresh: bool = False):  # noqa: ANN001
        # files: JSON 数组
        try:
            selected: List[str] = json.loads(files) if files else []
            selected = [p for p in selected if p and not p.startswith("..")]
        except Exception:
            selected = []

        pieces: List[str] = []
        if prefix_text and prefix_text.strip():
            pieces.append(prefix_text.strip())

        for rel in selected:
            try:
                txt = _read(rel)
                if txt and txt.strip():
                    pieces.append(txt.strip())
            except Exception:
                # 忽略非法或不存在
                pass

        out_text = "\n\n".join(pieces).strip()
        return (out_text,)

    @classmethod
    def IS_CHANGED(cls, **_kwargs):  # noqa: N802, ANN003
        # 允许强制刷新
        return float("nan")


NODE_CLASS_MAPPINGS = {
    GetLoraFileText.NAME: GetLoraFileText,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    GetLoraFileText.NAME: "Get Lora File Text",
}
