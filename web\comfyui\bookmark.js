"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bookmark = void 0;
const app_js_1 = require("scripts/app.js");
const base_node_js_1 = require("./base_node.js");
const key_events_services_js_1 = require("./services/key_events_services.js");
const constants_js_1 = require("./constants.js");
const utils_dom_js_1 = require("rgthree/common/utils_dom.js");
class Bookmark extends base_node_js_1.RgthreeBaseVirtualNode {
    get _collapsed_width() {
        return this.___collapsed_width;
    }
    set _collapsed_width(width) {
        const canvas = app_js_1.app.canvas;
        const ctx = canvas.canvas.getContext("2d");
        const oldFont = ctx.font;
        ctx.font = canvas.title_text_font;
        this.___collapsed_width = 40 + ctx.measureText(this.title).width;
        ctx.font = oldFont;
    }
    constructor(title = Bookmark.title) {
        super(title);
        this.comfyClass = constants_js_1.NodeTypesString.BOOKMARK;
        this.___collapsed_width = 0;
        this.isVirtualNode = true;
        this.serialize_widgets = true;
        const nextShortcutChar = getNextShortcut();
        this.addWidget("text", "shortcut_key", nextShortcutChar, (value, ...args) => {
            value = value.trim()[0] || "1";
        }, {
            y: 8,
        });
        this.addWidget("number", "zoom", 1, (value) => { }, {
            y: 8 + LiteGraph.NODE_WIDGET_HEIGHT + 4,
            max: 2,
            min: 0.5,
            precision: 2,
        });
        this.keypressBound = this.onKeypress.bind(this);
        this.title = "🔖";
        this.onConstructed();
    }
    get shortcutKey() {
        var _a, _b, _c;
        return (_c = (_b = (_a = this.widgets[0]) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.toLocaleLowerCase()) !== null && _c !== void 0 ? _c : "";
    }
    onAdded(graph) {
        key_events_services_js_1.SERVICE.addEventListener("keydown", this.keypressBound);
    }
    onRemoved() {
        key_events_services_js_1.SERVICE.removeEventListener("keydown", this.keypressBound);
    }
    onKeypress(event) {
        const originalEvent = event.detail.originalEvent;
        const target = originalEvent.target;
        if ((0, utils_dom_js_1.getClosestOrSelf)(target, 'input,textarea,[contenteditable="true"]')) {
            return;
        }
        if (key_events_services_js_1.SERVICE.areOnlyKeysDown(this.widgets[0].value, true)) {
            this.canvasToBookmark();
            originalEvent.preventDefault();
            originalEvent.stopPropagation();
        }
    }
    onMouseDown(event, pos, graphCanvas) {
        var _a;
        const input = (0, utils_dom_js_1.query)(".graphdialog > input.value");
        if (input && input.value === ((_a = this.widgets[0]) === null || _a === void 0 ? void 0 : _a.value)) {
            input.addEventListener("keydown", (e) => {
                key_events_services_js_1.SERVICE.handleKeyDownOrUp(e);
                e.preventDefault();
                e.stopPropagation();
                input.value = Object.keys(key_events_services_js_1.SERVICE.downKeys).join(" + ");
            });
        }
        return false;
    }
    canvasToBookmark() {
        var _a, _b;
        const canvas = app_js_1.app.canvas;
        if ((_a = canvas === null || canvas === void 0 ? void 0 : canvas.ds) === null || _a === void 0 ? void 0 : _a.offset) {
            canvas.ds.offset[0] = -this.pos[0] + 16;
            canvas.ds.offset[1] = -this.pos[1] + 40;
        }
        if (((_b = canvas === null || canvas === void 0 ? void 0 : canvas.ds) === null || _b === void 0 ? void 0 : _b.scale) != null) {
            canvas.ds.scale = Number(this.widgets[1].value || 1);
        }
        canvas.setDirty(true, true);
    }
}
exports.Bookmark = Bookmark;
Bookmark.type = constants_js_1.NodeTypesString.BOOKMARK;
Bookmark.title = constants_js_1.NodeTypesString.BOOKMARK;
Bookmark.slot_start_y = -20;
app_js_1.app.registerExtension({
    name: "rgthree.Bookmark",
    registerCustomNodes() {
        Bookmark.setUp();
    },
});
function isBookmark(node) {
    return node.type === constants_js_1.NodeTypesString.BOOKMARK;
}
function getExistingShortcuts() {
    const graph = app_js_1.app.graph;
    const bookmarkNodes = graph._nodes.filter(isBookmark);
    const usedShortcuts = new Set(bookmarkNodes.map((n) => n.shortcutKey));
    return usedShortcuts;
}
const SHORTCUT_DEFAULTS = "1234567890abcdefghijklmnopqrstuvwxyz".split("");
function getNextShortcut() {
    var _a;
    const existingShortcuts = getExistingShortcuts();
    return (_a = SHORTCUT_DEFAULTS.find((char) => !existingShortcuts.has(char))) !== null && _a !== void 0 ? _a : "1";
}
