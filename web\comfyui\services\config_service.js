"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SERVICE = void 0;
const config_js_1 = require("rgthree/config.js");
const shared_utils_js_1 = require("rgthree/common/shared_utils.js");
const rgthree_api_js_1 = require("rgthree/common/rgthree_api.js");
class ConfigService extends EventTarget {
    getConfigValue(key, def) {
        return (0, shared_utils_js_1.getObjectValue)(config_js_1.rgthreeConfig, key, def);
    }
    getFeatureValue(key, def) {
        key = "features." + key.replace(/^features\./, "");
        return (0, shared_utils_js_1.getObjectValue)(config_js_1.rgthreeConfig, key, def);
    }
    async setConfigValues(changed) {
        const body = new FormData();
        body.append("json", JSON.stringify(changed));
        const response = await rgthree_api_js_1.rgthreeApi.fetchJson("/config", { method: "POST", body });
        if (response.status === "ok") {
            for (const [key, value] of Object.entries(changed)) {
                (0, shared_utils_js_1.setObjectValue)(config_js_1.rgthreeConfig, key, value);
                this.dispatchEvent(new CustomEvent("config-change", { detail: { key, value } }));
            }
        }
        else {
            return false;
        }
        return true;
    }
}
exports.SERVICE = new ConfigService();
