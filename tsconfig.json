{
  "compilerOptions": {
    "target": "es2019",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    // "typeRoots": [
    //   "./ts/typings",
    // ],
    "baseUrl": "./",
    "paths": {
      // Main comfyui typedefs.
      "@comfyorg/frontend": ["node_modules/@comfyorg/comfyui-frontend-types/index.d.ts"],
      // ComfyUI augments old litegraph types with these module names, so we need to map them back
      // to itself to "see" these augmentations in our code.
      "@/lib/litegraph/src/types/widgets": ["node_modules/@comfyorg/comfyui-frontend-types/index.d.ts"],
      "@/lib/litegraph/src/interfaces": ["node_modules/@comfyorg/comfyui-frontend-types/index.d.ts"],
      "@/lib/litegraph/src/litegraph": ["node_modules/@comfyorg/comfyui-frontend-types/index.d.ts"],
      // Tree Sitter, for parsing python in js.
      "web-tree-sitter": ["node_modules/web-tree-sitter/web-tree-sitter.d.ts"],
      "typings/*": ["src_web/typings/*"],
      "rgthree/common/*": ["src_web/common/*"],
      "rgthree/lib/*": ["src_web/lib/*"],
      "node_modules": ["node_modules/*"],
      "scripts/*":  ["src_web/scripts_comfy/*"],
    },
    "outDir": "web/",
    "removeComments": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "exactOptionalPropertyTypes": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "allowUnusedLabels": true,
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "types": []
  },
  "include": [
    "src_web/*.ts", "src_web/**/*.ts", "src_web/typings/index.d.ts",
  ],
  "exclude": [
    "**/*.spec.ts",
    "**/*.d.ts",
    "node_modules/**/*.ts"
  ]
}
